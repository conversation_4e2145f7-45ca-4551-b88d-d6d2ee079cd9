{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/implementations/client-app-impl.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { WebAuthnError, startAuthentication, startRegistration } from \"@simplewebauthn/browser\";\nimport { KnownErrors, StackClientInterface } from \"@stackframe/stack-shared\";\nimport { ContactChannelsCrud } from \"@stackframe/stack-shared/dist/interface/crud/contact-channels\";\nimport { CurrentUserCrud } from \"@stackframe/stack-shared/dist/interface/crud/current-user\";\nimport { Team<PERSON><PERSON><PERSON><PERSON>sCrud, UserApiKeysCrud, teamApiKeysCreateOutputSchema, userApiKeysCreateOutputSchema } from \"@stackframe/stack-shared/dist/interface/crud/project-api-keys\";\nimport { ProjectPermissionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/project-permissions\";\nimport { ClientProjectsCrud } from \"@stackframe/stack-shared/dist/interface/crud/projects\";\nimport { SessionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/sessions\";\nimport { TeamInvitationCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-invitation\";\nimport { TeamMemberProfilesCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-member-profiles\";\nimport { TeamPermissionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-permissions\";\nimport { TeamsCrud } from \"@stackframe/stack-shared/dist/interface/crud/teams\";\nimport { UsersCrud } from \"@stackframe/stack-shared/dist/interface/crud/users\";\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { scrambleDuringCompileTime } from \"@stackframe/stack-shared/dist/utils/compile-time\";\nimport { isBrowserLike } from \"@stackframe/stack-shared/dist/utils/env\";\nimport { StackAssertionError, captureError, throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { DependenciesMap } from \"@stackframe/stack-shared/dist/utils/maps\";\nimport { ProviderType } from \"@stackframe/stack-shared/dist/utils/oauth\";\nimport { deepPlainEquals, omit } from \"@stackframe/stack-shared/dist/utils/objects\";\nimport { neverResolve, runAsynchronously, wait } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { suspend, suspendIfSsr } from \"@stackframe/stack-shared/dist/utils/react\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { Store, storeLock } from \"@stackframe/stack-shared/dist/utils/stores\";\nimport { deindent, mergeScopeStrings } from \"@stackframe/stack-shared/dist/utils/strings\";\nimport { getRelativePart, isRelative } from \"@stackframe/stack-shared/dist/utils/urls\";\nimport { generateUuid } from \"@stackframe/stack-shared/dist/utils/uuids\";\nimport * as cookie from \"cookie\";\nimport type * as yup from \"yup\";\nimport { constructRedirectUrl } from \"../../../../utils/url\";\nimport { addNewOAuthProviderOrScope, callOAuthCallback, signInWithOAuth } from \"../../../auth\";\nimport { CookieHelper, createBrowserCookieHelper, createCookieHelper, createPlaceholderCookieHelper, deleteCookieClient, getCookieClient, setOrDeleteCookie, setOrDeleteCookieClient } from \"../../../cookie\";\nimport { ApiKey, ApiKeyCreationOptions, ApiKeyUpdateOptions, apiKeyCreationOptionsToCrud } from \"../../api-keys\";\nimport { GetUserOptions, HandlerUrls, OAuthScopesOnSignIn, RedirectMethod, RedirectToOptions, RequestLike, TokenStoreInit, stackAppInternalsSymbol } from \"../../common\";\nimport { OAuthConnection } from \"../../connected-accounts\";\nimport { ContactChannel, ContactChannelCreateOptions, ContactChannelUpdateOptions, contactChannelCreateOptionsToCrud, contactChannelUpdateOptionsToCrud } from \"../../contact-channels\";\nimport { TeamPermission } from \"../../permissions\";\nimport { AdminOwnedProject, AdminProjectUpdateOptions, Project, adminProjectCreateOptionsToCrud } from \"../../projects\";\nimport { EditableTeamMemberProfile, Team, TeamCreateOptions, TeamInvitation, TeamUpdateOptions, TeamUser, teamCreateOptionsToCrud, teamUpdateOptionsToCrud } from \"../../teams\";\nimport { ActiveSession, Auth, BaseUser, CurrentUser, InternalUserExtra, ProjectCurrentUser, UserExtra, UserUpdateOptions, userUpdateOptionsToCrud } from \"../../users\";\nimport { StackClientApp, StackClientAppConstructorOptions, StackClientAppJson } from \"../interfaces/client-app\";\nimport { _StackAdminAppImplIncomplete } from \"./admin-app-impl\";\nimport { TokenObject, clientVersion, createCache, createCacheBySession, createEmptyTokenStore, getBaseUrl, getDefaultExtraRequestHeaders, getDefaultProjectId, getDefaultPublishableClientKey, getUrls, } from \"./common\";\n\nimport React, { useCallback, useMemo } from \"react\"; // THIS_LINE_PLATFORM react-like\nimport { useAsyncCache } from \"./common\"; // THIS_LINE_PLATFORM react-like\n\nlet isReactServer = false;\n\n// hack to make sure process is defined in non-node environments\nconst process = (globalThis as any).process ?? { env: {} }; // THIS_LINE_PLATFORM js react\n\n\nconst allClientApps = new Map<string, [checkString: string | undefined, app: StackClientApp<any, any>]>();\n\nexport class _StackClientAppImplIncomplete<HasTokenStore extends boolean, ProjectId extends string = string> implements StackClientApp<HasTokenStore, ProjectId> {\n  /**\n   * There is a circular dependency between the admin app and the client app, as the former inherits from the latter and\n   * the latter needs to use the former when creating a new instance of an internal project.\n   *\n   * To break it, we set the admin app here lazily instead of importing it directly. This variable is set by ./index.ts,\n   * which imports both this file and ./admin-app-impl.ts.\n   */\n  static readonly LazyStackAdminAppImpl: { value: typeof import(\"./admin-app-impl\")._StackAdminAppImplIncomplete | undefined } = { value: undefined };\n\n  protected _uniqueIdentifier: string | undefined = undefined;\n  protected _interface: StackClientInterface;\n  protected readonly _tokenStoreInit: TokenStoreInit<HasTokenStore>;\n  protected readonly _redirectMethod: RedirectMethod | undefined;\n  protected readonly _urlOptions: Partial<HandlerUrls>;\n  protected readonly _oauthScopesOnSignIn: Partial<OAuthScopesOnSignIn>;\n\n  private __DEMO_ENABLE_SLIGHT_FETCH_DELAY = false;\n  private readonly _ownedAdminApps = new DependenciesMap<[InternalSession, string], _StackAdminAppImplIncomplete<false, string>>();\n\n  private readonly _currentUserCache = createCacheBySession(async (session) => {\n    if (this.__DEMO_ENABLE_SLIGHT_FETCH_DELAY) {\n      await wait(2000);\n    }\n    if (session.isKnownToBeInvalid()) {\n      // let's save ourselves a network request\n      //\n      // this also makes a certain race condition less likely to happen. particularly, it's quite common for code to\n      // look like this:\n      //\n      //     const user = await useUser({ or: \"required\" });\n      //     const something = user.useSomething();\n      //\n      // now, let's say the session is invalidated. this will trigger a refresh to refresh both the user and the\n      // something. however, it's not guaranteed that the user will return first, so useUser might still return a\n      // user object while the something request has already completed (and failed, because the session is invalid).\n      // by returning null quickly here without a request, it is very very likely for the user request to complete\n      // first.\n      //\n      // TODO HACK: the above is a bit of a hack, and we should probably think of more consistent ways to handle this.\n      // it also only works for the user endpoint, and only if the session is known to be invalid.\n      return null;\n    }\n    return await this._interface.getClientUserByToken(session);\n  });\n  private readonly _currentProjectCache = createCache(async () => {\n    return Result.orThrow(await this._interface.getClientProject());\n  });\n  private readonly _ownedProjectsCache = createCacheBySession(async (session) => {\n    return await this._interface.listProjects(session);\n  });\n  private readonly _currentUserPermissionsCache = createCacheBySession<\n    [string, boolean],\n    TeamPermissionsCrud['Client']['Read'][]\n  >(async (session, [teamId, recursive]) => {\n    return await this._interface.listCurrentUserTeamPermissions({ teamId, recursive }, session);\n  });\n  private readonly _currentUserProjectPermissionsCache = createCacheBySession<\n    [boolean],\n    ProjectPermissionsCrud['Client']['Read'][]\n  >(async (session, [recursive]) => {\n    return await this._interface.listCurrentUserProjectPermissions({ recursive }, session);\n  });\n  private readonly _currentUserTeamsCache = createCacheBySession(async (session) => {\n    return await this._interface.listCurrentUserTeams(session);\n  });\n  private readonly _currentUserOAuthConnectionAccessTokensCache = createCacheBySession<[string, string], { accessToken: string } | null>(\n    async (session, [providerId, scope]) => {\n      try {\n        const result = await this._interface.createProviderAccessToken(providerId, scope || \"\", session);\n        return { accessToken: result.access_token };\n      } catch (err) {\n        if (!(KnownErrors.OAuthConnectionDoesNotHaveRequiredScope.isInstance(err) || KnownErrors.OAuthConnectionNotConnectedToUser.isInstance(err))) {\n          throw err;\n        }\n      }\n      return null;\n    }\n  );\n  private readonly _currentUserOAuthConnectionCache = createCacheBySession<[ProviderType, string, boolean], OAuthConnection | null>(\n    async (session, [providerId, scope, redirect]) => {\n      return await this._getUserOAuthConnectionCacheFn({\n        getUser: async () => Result.orThrow(await this._currentUserCache.getOrWait([session], \"write-only\")),\n        getOrWaitOAuthToken: async () => Result.orThrow(await this._currentUserOAuthConnectionAccessTokensCache.getOrWait([session, providerId, scope || \"\"] as const, \"write-only\")),\n        useOAuthToken: () => useAsyncCache(this._currentUserOAuthConnectionAccessTokensCache, [session, providerId, scope || \"\"] as const, \"useOAuthToken\"),\n        providerId,\n        scope,\n        redirect,\n        session,\n      });\n    }\n  );\n  private readonly _teamMemberProfilesCache = createCacheBySession<[string], TeamMemberProfilesCrud['Client']['Read'][]>(\n    async (session, [teamId]) => {\n      return await this._interface.listTeamMemberProfiles({ teamId }, session);\n    }\n  );\n  private readonly _teamInvitationsCache = createCacheBySession<[string], TeamInvitationCrud['Client']['Read'][]>(\n    async (session, [teamId]) => {\n      return await this._interface.listTeamInvitations({ teamId }, session);\n    }\n  );\n  private readonly _currentUserTeamProfileCache = createCacheBySession<[string], TeamMemberProfilesCrud['Client']['Read']>(\n    async (session, [teamId]) => {\n      return await this._interface.getTeamMemberProfile({ teamId, userId: 'me' }, session);\n    }\n  );\n  private readonly _clientContactChannelsCache = createCacheBySession<[], ContactChannelsCrud['Client']['Read'][]>(\n    async (session) => {\n      return await this._interface.listClientContactChannels(session);\n    }\n  );\n\n  private readonly _userApiKeysCache = createCacheBySession<[], UserApiKeysCrud['Client']['Read'][]>(\n    async (session) => {\n      const results = await this._interface.listProjectApiKeys({ user_id: 'me' }, session, \"client\");\n      return results as UserApiKeysCrud['Client']['Read'][];\n    }\n  );\n\n  private readonly _teamApiKeysCache = createCacheBySession<[string], TeamApiKeysCrud['Client']['Read'][]>(\n    async (session, [teamId]) => {\n      const results = await this._interface.listProjectApiKeys({ team_id: teamId }, session, \"client\");\n      return results as TeamApiKeysCrud['Client']['Read'][];\n    }\n  );\n\n  private _anonymousSignUpInProgress: Promise<{ accessToken: string, refreshToken: string }> | null = null;\n\n  protected async _createCookieHelper(): Promise<CookieHelper> {\n    if (this._tokenStoreInit === 'nextjs-cookie' || this._tokenStoreInit === 'cookie') {\n      return await createCookieHelper();\n    } else {\n      return await createPlaceholderCookieHelper();\n    }\n  }\n\n  protected async _getUserOAuthConnectionCacheFn(options: {\n    getUser: () => Promise<CurrentUserCrud['Client']['Read'] | null>,\n    getOrWaitOAuthToken: () => Promise<{ accessToken: string } | null>,\n    useOAuthToken: () => { accessToken: string } | null,\n    providerId: ProviderType,\n    scope: string | null,\n  } & ({ redirect: true, session: InternalSession | null } | { redirect: false }),) {\n    const user = await options.getUser();\n    let hasConnection = true;\n    if (!user || !user.oauth_providers.find((p) => p.id === options.providerId)) {\n      hasConnection = false;\n    }\n\n    const token = await options.getOrWaitOAuthToken();\n    if (!token) {\n      hasConnection = false;\n    }\n\n    if (!hasConnection && options.redirect) {\n      if (!options.session) {\n        throw new Error(deindent`\n          Cannot add new scopes to a user that is not a CurrentUser. Please ensure that you are calling this function on a CurrentUser object, or remove the 'or: redirect' option.\n\n          Often, you can solve this by calling this function in the browser instead, or by removing the 'or: redirect' option and dealing with the case where the user doesn't have enough permissions.\n        `);\n      }\n      await addNewOAuthProviderOrScope(\n          this._interface,\n          {\n            provider: options.providerId,\n            redirectUrl: this.urls.oauthCallback,\n            errorRedirectUrl: this.urls.error,\n            providerScope: mergeScopeStrings(options.scope || \"\", (this._oauthScopesOnSignIn[options.providerId] ?? []).join(\" \")),\n          },\n          options.session,\n        );\n      return await neverResolve();\n    } else if (!hasConnection) {\n      return null;\n    }\n\n    return {\n      id: options.providerId,\n      async getAccessToken() {\n        const result = await options.getOrWaitOAuthToken();\n        if (!result) {\n          throw new StackAssertionError(\"No access token available\");\n        }\n        return result;\n      },\n      useAccessToken() {\n        const result = options.useOAuthToken();\n        if (!result) {\n          throw new StackAssertionError(\"No access token available\");\n        }\n        return result;\n      }\n    };\n  }\n\n  constructor(protected readonly _options:\n    & {\n      uniqueIdentifier?: string,\n      checkString?: string,\n    }\n    & (\n      | StackClientAppConstructorOptions<HasTokenStore, ProjectId>\n      | Exclude<StackClientAppConstructorOptions<HasTokenStore, ProjectId>, \"baseUrl\" | \"projectId\" | \"publishableClientKey\"> & {\n        interface: StackClientInterface,\n      }\n    )\n  ) {\n    if (!_StackClientAppImplIncomplete.LazyStackAdminAppImpl.value) {\n      throw new StackAssertionError(\"Admin app implementation not initialized. Did you import the _StackClientApp from stack-app/apps/implementations/index.ts? You can't import it directly from ./apps/implementations/client-app-impl.ts as that causes a circular dependency (see the comment at _LazyStackAdminAppImpl for more details).\");\n    }\n\n    if (\"interface\" in _options) {\n      this._interface = _options.interface;\n    } else {\n      this._interface = new StackClientInterface({\n        getBaseUrl: () => getBaseUrl(_options.baseUrl),\n        extraRequestHeaders: _options.extraRequestHeaders ?? getDefaultExtraRequestHeaders(),\n        projectId: _options.projectId ?? getDefaultProjectId(),\n        clientVersion,\n        publishableClientKey: _options.publishableClientKey ?? getDefaultPublishableClientKey(),\n        prepareRequest: async () => {\n        }\n      });\n    }\n\n    this._tokenStoreInit = _options.tokenStore;\n    this._redirectMethod = _options.redirectMethod || \"none\";\n    this._urlOptions = _options.urls ?? {};\n    this._oauthScopesOnSignIn = _options.oauthScopesOnSignIn ?? {};\n\n    if (_options.uniqueIdentifier) {\n      this._uniqueIdentifier = _options.uniqueIdentifier;\n      this._initUniqueIdentifier();\n    }\n  }\n\n  protected _initUniqueIdentifier() {\n    if (!this._uniqueIdentifier) {\n      throw new StackAssertionError(\"Unique identifier not initialized\");\n    }\n    if (allClientApps.has(this._uniqueIdentifier)) {\n      throw new StackAssertionError(\"A Stack client app with the same unique identifier already exists\");\n    }\n    allClientApps.set(this._uniqueIdentifier, [this._options.checkString ?? undefined, this]);\n  }\n\n  /**\n   * Cloudflare workers does not allow use of randomness on the global scope (on which the Stack app is probably\n   * initialized). For that reason, we generate the unique identifier lazily when it is first needed instead of in the\n   * constructor.\n   */\n  protected _getUniqueIdentifier() {\n    if (!this._uniqueIdentifier) {\n      this._uniqueIdentifier = generateUuid();\n      this._initUniqueIdentifier();\n    }\n    return this._uniqueIdentifier!;\n  }\n\n  protected async _checkFeatureSupport(name: string, options: any) {\n    return await this._interface.checkFeatureSupport({ ...options, name });\n  }\n\n  protected _useCheckFeatureSupport(name: string, options: any): never {\n    runAsynchronously(this._checkFeatureSupport(name, options));\n    throw new StackAssertionError(`${name} is not currently supported. Please reach out to Stack support for more information.`);\n  }\n\n  protected _memoryTokenStore = createEmptyTokenStore();\n  protected _nextServerCookiesTokenStores = new WeakMap<object, Store<TokenObject>>();\n  protected _requestTokenStores = new WeakMap<RequestLike, Store<TokenObject>>();\n  protected _storedBrowserCookieTokenStore: Store<TokenObject> | null = null;\n  protected get _refreshTokenCookieName() {\n    return `stack-refresh-${this.projectId}`;\n  }\n  protected _getTokensFromCookies(cookies: { refreshTokenCookie: string | null, accessTokenCookie: string | null }): TokenObject {\n    const refreshToken = cookies.refreshTokenCookie;\n    const accessTokenObject = cookies.accessTokenCookie?.startsWith('[\\\"') ? JSON.parse(cookies.accessTokenCookie) : null;  // gotta check for validity first for backwards-compat, and also in case someone messes with the cookie value\n    const accessToken = accessTokenObject && refreshToken === accessTokenObject[0] ? accessTokenObject[1] : null;  // if the refresh token has changed, the access token is invalid\n    return {\n      refreshToken,\n      accessToken,\n    };\n  }\n  protected get _accessTokenCookieName() {\n    // The access token, unlike the refresh token, should not depend on the project ID. We never want to store the\n    // access token in cookies more than once because of how big it is (there's a limit of 4096 bytes for all cookies\n    // together). This means that, if you have multiple projects on the same domain, some of them will need to refetch\n    // the access token on page reload.\n    return `stack-access`;\n  }\n  protected _getBrowserCookieTokenStore(): Store<TokenObject> {\n    if (!isBrowserLike()) {\n      throw new Error(\"Cannot use cookie token store on the server!\");\n    }\n\n    if (this._storedBrowserCookieTokenStore === null) {\n      const getCurrentValue = (old: TokenObject | null) => {\n        const tokens = this._getTokensFromCookies({\n          refreshTokenCookie: getCookieClient(this._refreshTokenCookieName) ?? getCookieClient('stack-refresh'),  // keep old cookie name for backwards-compatibility\n          accessTokenCookie: getCookieClient(this._accessTokenCookieName),\n        });\n        return {\n          refreshToken: tokens.refreshToken,\n          accessToken: tokens.accessToken ?? (old?.refreshToken === tokens.refreshToken ? old.accessToken : null),\n        };\n      };\n      this._storedBrowserCookieTokenStore = new Store<TokenObject>(getCurrentValue(null));\n      let hasSucceededInWriting = true;\n\n      setInterval(() => {\n        if (hasSucceededInWriting) {\n          const oldValue = this._storedBrowserCookieTokenStore!.get();\n          const currentValue = getCurrentValue(oldValue);\n          if (!deepPlainEquals(currentValue, oldValue)) {\n            this._storedBrowserCookieTokenStore!.set(currentValue);\n          }\n        }\n      }, 100);\n      this._storedBrowserCookieTokenStore.onChange((value) => {\n        try {\n          setOrDeleteCookieClient(this._refreshTokenCookieName, value.refreshToken, { maxAge: 60 * 60 * 24 * 365 });\n          setOrDeleteCookieClient(this._accessTokenCookieName, value.accessToken ? JSON.stringify([value.refreshToken, value.accessToken]) : null, { maxAge: 60 * 60 * 24 });\n          deleteCookieClient('stack-refresh');  // delete cookie name from previous versions (for backwards-compatibility)\n          hasSucceededInWriting = true;\n        } catch (e) {\n          if (!isBrowserLike()) {\n            // Setting cookies inside RSCs is not allowed, so we just ignore it\n            hasSucceededInWriting = false;\n          } else {\n            throw e;\n          }\n        }\n      });\n    }\n\n    return this._storedBrowserCookieTokenStore;\n  };\n  protected _getOrCreateTokenStore(cookieHelper: CookieHelper, overrideTokenStoreInit?: TokenStoreInit): Store<TokenObject> {\n    const tokenStoreInit = overrideTokenStoreInit === undefined ? this._tokenStoreInit : overrideTokenStoreInit;\n\n    switch (tokenStoreInit) {\n      case \"cookie\": {\n        return this._getBrowserCookieTokenStore();\n      }\n      case \"nextjs-cookie\": {\n        if (isBrowserLike()) {\n          return this._getBrowserCookieTokenStore();\n        } else {\n          const tokens = this._getTokensFromCookies({\n            refreshTokenCookie: cookieHelper.get(this._refreshTokenCookieName) ?? cookieHelper.get('stack-refresh'),  // keep old cookie name for backwards-compatibility\n            accessTokenCookie: cookieHelper.get(this._accessTokenCookieName),\n          });\n          const store = new Store<TokenObject>(tokens);\n          store.onChange((value) => {\n            runAsynchronously(async () => {\n              // TODO HACK this is a bit of a hack; while the order happens to work in practice (because the only actual\n              // async operation is waiting for the `cookies()` to resolve which always happens at the same time during\n              // the same request), it's not guaranteed to be free of race conditions if there are many updates happening\n              // at the same time\n              //\n              // instead, we should create a per-request cookie helper outside of the store onChange and reuse that\n              //\n              // but that's kinda hard to do because Next.js doesn't expose a documented way to find out which request\n              // we're currently processing, and hence we can't find out which per-request cookie helper to use\n              //\n              // so hack it is\n              await Promise.all([\n                setOrDeleteCookie(this._refreshTokenCookieName, value.refreshToken, { maxAge: 60 * 60 * 24 * 365, noOpIfServerComponent: true }),\n                setOrDeleteCookie(this._accessTokenCookieName, value.accessToken ? JSON.stringify([value.refreshToken, value.accessToken]) : null, { maxAge: 60 * 60 * 24, noOpIfServerComponent: true }),\n              ]);\n            });\n          });\n          return store;\n        }\n      }\n      case \"memory\": {\n        return this._memoryTokenStore;\n      }\n      default: {\n        if (tokenStoreInit === null) {\n          return createEmptyTokenStore();\n        } else if (typeof tokenStoreInit === \"object\" && \"headers\" in tokenStoreInit) {\n          if (this._requestTokenStores.has(tokenStoreInit)) return this._requestTokenStores.get(tokenStoreInit)!;\n\n          // x-stack-auth header\n          const stackAuthHeader = tokenStoreInit.headers.get(\"x-stack-auth\");\n          if (stackAuthHeader) {\n            let parsed;\n            try {\n              parsed = JSON.parse(stackAuthHeader);\n              if (typeof parsed !== \"object\") throw new Error(\"x-stack-auth header must be a JSON object\");\n              if (parsed === null) throw new Error(\"x-stack-auth header must not be null\");\n            } catch (e) {\n              throw new Error(`Invalid x-stack-auth header: ${stackAuthHeader}`, { cause: e });\n            }\n            return this._getOrCreateTokenStore(cookieHelper, {\n              accessToken: parsed.accessToken ?? null,\n              refreshToken: parsed.refreshToken ?? null,\n            });\n          }\n\n          // read from cookies\n          const cookieHeader = tokenStoreInit.headers.get(\"cookie\");\n          const parsed = cookie.parse(cookieHeader || \"\");\n          const res = new Store<TokenObject>({\n            refreshToken: parsed[this._refreshTokenCookieName] || parsed['stack-refresh'] || null,  // keep old cookie name for backwards-compatibility\n            accessToken: parsed[this._accessTokenCookieName] || null,\n          });\n          this._requestTokenStores.set(tokenStoreInit, res);\n          return res;\n        } else if (\"accessToken\" in tokenStoreInit || \"refreshToken\" in tokenStoreInit) {\n          return new Store<TokenObject>({\n            refreshToken: tokenStoreInit.refreshToken,\n            accessToken: tokenStoreInit.accessToken,\n          });\n        }\n\n        throw new Error(`Invalid token store ${tokenStoreInit}`);\n      }\n    }\n  }\n\n  protected _useTokenStore(overrideTokenStoreInit?: TokenStoreInit): Store<TokenObject> {\n    suspendIfSsr();\n    const cookieHelper = createBrowserCookieHelper();\n    const tokenStore = this._getOrCreateTokenStore(cookieHelper, overrideTokenStoreInit);\n    return tokenStore;\n  }\n\n  /**\n   * A map from token stores and session keys to sessions.\n   *\n   * This isn't just a map from session keys to sessions for two reasons:\n   *\n   * - So we can garbage-collect Session objects when the token store is garbage-collected\n   * - So different token stores are separated and don't leak information between each other, eg. if the same user sends two requests to the same server they should get a different session object\n   */\n  private _sessionsByTokenStoreAndSessionKey = new WeakMap<Store<TokenObject>, Map<string, InternalSession>>();\n  protected _getSessionFromTokenStore(tokenStore: Store<TokenObject>): InternalSession {\n    const tokenObj = tokenStore.get();\n    const sessionKey = InternalSession.calculateSessionKey(tokenObj);\n    const existing = sessionKey ? this._sessionsByTokenStoreAndSessionKey.get(tokenStore)?.get(sessionKey) : null;\n    if (existing) return existing;\n\n    const session = this._interface.createSession({\n      refreshToken: tokenObj.refreshToken,\n      accessToken: tokenObj.accessToken,\n    });\n    session.onAccessTokenChange((newAccessToken) => {\n      tokenStore.update((old) => ({\n        ...old,\n        accessToken: newAccessToken?.token ?? null\n      }));\n    });\n    session.onInvalidate(() => {\n      tokenStore.update((old) => ({\n        ...old,\n        accessToken: null,\n        refreshToken: null,\n      }));\n    });\n\n    let sessionsBySessionKey = this._sessionsByTokenStoreAndSessionKey.get(tokenStore) ?? new Map();\n    this._sessionsByTokenStoreAndSessionKey.set(tokenStore, sessionsBySessionKey);\n    sessionsBySessionKey.set(sessionKey, session);\n    return session;\n  }\n\n  protected async _getSession(overrideTokenStoreInit?: TokenStoreInit): Promise<InternalSession> {\n    const tokenStore = this._getOrCreateTokenStore(await this._createCookieHelper(), overrideTokenStoreInit);\n    return this._getSessionFromTokenStore(tokenStore);\n  }\n\n  protected _useSession(overrideTokenStoreInit?: TokenStoreInit): InternalSession {\n    const tokenStore = this._useTokenStore(overrideTokenStoreInit);\n    const subscribe = useCallback((cb: () => void) => {\n      const { unsubscribe } = tokenStore.onChange(() => {\n        cb();\n      });\n      return unsubscribe;\n    }, [tokenStore]);\n    const getSnapshot = useCallback(() => this._getSessionFromTokenStore(tokenStore), [tokenStore]);\n    return React.useSyncExternalStore(subscribe, getSnapshot, getSnapshot);\n  }\n\n  protected async _signInToAccountWithTokens(tokens: { accessToken: string | null, refreshToken: string }) {\n    if (!(\"accessToken\" in tokens) || !(\"refreshToken\" in tokens)) {\n      throw new StackAssertionError(\"Invalid tokens object; can't sign in with this\", { tokens });\n    }\n    const tokenStore = this._getOrCreateTokenStore(await this._createCookieHelper());\n    tokenStore.set(tokens);\n  }\n\n  protected _hasPersistentTokenStore(overrideTokenStoreInit?: TokenStoreInit): this is StackClientApp<true, ProjectId> {\n    return (overrideTokenStoreInit !== undefined ? overrideTokenStoreInit : this._tokenStoreInit) !== null;\n  }\n\n  protected _ensurePersistentTokenStore(overrideTokenStoreInit?: TokenStoreInit): asserts this is StackClientApp<true, ProjectId>  {\n    if (!this._hasPersistentTokenStore(overrideTokenStoreInit)) {\n      throw new Error(\"Cannot call this function on a Stack app without a persistent token store. Make sure the tokenStore option on the constructor is set to a non-null value when initializing Stack.\\n\\nStack uses token stores to access access tokens of the current user. For example, on web frontends it is commonly the string value 'cookies' for cookie storage.\");\n    }\n  }\n\n  protected _isInternalProject(): this is { projectId: \"internal\" } {\n    return this.projectId === \"internal\";\n  }\n\n  protected _ensureInternalProject(): asserts this is { projectId: \"internal\" } {\n    if (!this._isInternalProject()) {\n      throw new Error(\"Cannot call this function on a Stack app with a project ID other than 'internal'.\");\n    }\n  }\n\n  protected _clientProjectFromCrud(crud: ClientProjectsCrud['Client']['Read']): Project {\n    return {\n      id: crud.id,\n      displayName: crud.display_name,\n      config: {\n        signUpEnabled: crud.config.sign_up_enabled,\n        credentialEnabled: crud.config.credential_enabled,\n        magicLinkEnabled: crud.config.magic_link_enabled,\n        passkeyEnabled: crud.config.passkey_enabled,\n        clientTeamCreationEnabled: crud.config.client_team_creation_enabled,\n        clientUserDeletionEnabled: crud.config.client_user_deletion_enabled,\n        allowTeamApiKeys: crud.config.allow_team_api_keys,\n        allowUserApiKeys: crud.config.allow_user_api_keys,\n        oauthProviders: crud.config.enabled_oauth_providers.map((p) => ({\n          id: p.id,\n        })),\n      }\n    };\n  }\n\n  protected _clientPermissionFromCrud(crud: TeamPermissionsCrud['Client']['Read'] | ProjectPermissionsCrud['Client']['Read']): TeamPermission {\n    return {\n      id: crud.id,\n    };\n  }\n\n  protected _clientTeamUserFromCrud(crud: TeamMemberProfilesCrud['Client']['Read']): TeamUser {\n    return {\n      id: crud.user_id,\n      teamProfile: {\n        displayName: crud.display_name,\n        profileImageUrl: crud.profile_image_url,\n      }\n    };\n  }\n\n  protected _clientTeamInvitationFromCrud(session: InternalSession, crud: TeamInvitationCrud['Client']['Read']): TeamInvitation {\n    return {\n      id: crud.id,\n      recipientEmail: crud.recipient_email,\n      expiresAt: new Date(crud.expires_at_millis),\n      revoke: async () => {\n        await this._interface.revokeTeamInvitation(crud.id, crud.team_id, session);\n        await this._teamInvitationsCache.refresh([session, crud.team_id]);\n      },\n    };\n  }\n\n  protected _baseApiKeyFromCrud(\n    crud: TeamApiKeysCrud['Client']['Read'] | UserApiKeysCrud['Client']['Read'] | yup.InferType<typeof teamApiKeysCreateOutputSchema> | yup.InferType<typeof userApiKeysCreateOutputSchema>\n  ): Omit<ApiKey<\"user\", boolean>, \"revoke\" | \"update\"> | Omit<ApiKey<\"team\", boolean>, \"revoke\" | \"update\"> {\n    return {\n      id: crud.id,\n      description: crud.description,\n      expiresAt: crud.expires_at_millis ? new Date(crud.expires_at_millis) : undefined,\n      manuallyRevokedAt: crud.manually_revoked_at_millis ? new Date(crud.manually_revoked_at_millis) : null,\n      createdAt: new Date(crud.created_at_millis),\n      ...(crud.type === \"team\" ? { type: \"team\", teamId: crud.team_id } : { type: \"user\", userId: crud.user_id }),\n      value: typeof crud.value === \"string\" ? crud.value : {\n        lastFour: crud.value.last_four,\n      },\n      isValid: function() {\n        return this.whyInvalid() === null;\n      },\n      whyInvalid: function() {\n        if (this.manuallyRevokedAt) {\n          return \"manually-revoked\";\n        }\n        if (this.expiresAt && this.expiresAt < new Date()) {\n          return \"expired\";\n        }\n        return null;\n      },\n    };\n  }\n\n\n  protected _clientApiKeyFromCrud(session: InternalSession, crud: TeamApiKeysCrud['Client']['Read']): ApiKey<\"team\">;\n  protected _clientApiKeyFromCrud(session: InternalSession, crud: UserApiKeysCrud['Client']['Read']): ApiKey<\"user\">;\n  protected _clientApiKeyFromCrud(session: InternalSession, crud: yup.InferType<typeof teamApiKeysCreateOutputSchema>): ApiKey<\"team\", true>;\n  protected _clientApiKeyFromCrud(session: InternalSession, crud: yup.InferType<typeof userApiKeysCreateOutputSchema>): ApiKey<\"user\", true>;\n  protected _clientApiKeyFromCrud(session: InternalSession, crud: TeamApiKeysCrud['Client']['Read'] | UserApiKeysCrud['Client']['Read'] | yup.InferType<typeof teamApiKeysCreateOutputSchema> | yup.InferType<typeof userApiKeysCreateOutputSchema>): ApiKey<\"user\" | \"team\", boolean> {\n    return {\n      ...this._baseApiKeyFromCrud(crud),\n      async revoke() {\n        await this.update({ revoked: true });\n      },\n      update: async (options: ApiKeyUpdateOptions) => {\n        await this._interface.updateProjectApiKey(crud.type === \"team\" ? { team_id: crud.team_id } : { user_id: crud.user_id }, crud.id, options, session, \"client\");\n        if (crud.type === \"team\") {\n          await this._teamApiKeysCache.refresh([session, crud.team_id]);\n        } else {\n          await this._userApiKeysCache.refresh([session]);\n        }\n      },\n    };\n  }\n\n  protected _clientTeamFromCrud(crud: TeamsCrud['Client']['Read'], session: InternalSession): Team {\n    const app = this;\n    return {\n      id: crud.id,\n      displayName: crud.display_name,\n      profileImageUrl: crud.profile_image_url,\n      clientMetadata: crud.client_metadata,\n      clientReadOnlyMetadata: crud.client_read_only_metadata,\n      async inviteUser(options: { email: string, callbackUrl?: string }) {\n        await app._interface.sendTeamInvitation({\n          teamId: crud.id,\n          email: options.email,\n          session,\n          callbackUrl: options.callbackUrl ?? constructRedirectUrl(app.urls.teamInvitation, \"callbackUrl\"),\n        });\n        await app._teamInvitationsCache.refresh([session, crud.id]);\n      },\n      async listUsers() {\n        const result = Result.orThrow(await app._teamMemberProfilesCache.getOrWait([session, crud.id], \"write-only\"));\n        return result.map((crud) => app._clientTeamUserFromCrud(crud));\n      },\n      useUsers() {\n        const result = useAsyncCache(app._teamMemberProfilesCache, [session, crud.id] as const, \"team.useUsers()\");\n        return result.map((crud) => app._clientTeamUserFromCrud(crud));\n      },\n      async listInvitations() {\n        const result = Result.orThrow(await app._teamInvitationsCache.getOrWait([session, crud.id], \"write-only\"));\n        return result.map((crud) => app._clientTeamInvitationFromCrud(session, crud));\n      },\n      useInvitations() {\n        const result = useAsyncCache(app._teamInvitationsCache, [session, crud.id] as const, \"team.useInvitations()\");\n        return result.map((crud) => app._clientTeamInvitationFromCrud(session, crud));\n      },\n      async update(data: TeamUpdateOptions){\n        await app._interface.updateTeam({ data: teamUpdateOptionsToCrud(data), teamId: crud.id }, session);\n        await app._currentUserTeamsCache.refresh([session]);\n      },\n      async delete() {\n        await app._interface.deleteTeam(crud.id, session);\n        await app._currentUserTeamsCache.refresh([session]);\n      },\n\n      useApiKeys() {\n        const result = useAsyncCache(app._teamApiKeysCache, [session, crud.id] as const, \"team.useApiKeys()\");\n        return result.map((crud) => app._clientApiKeyFromCrud(session, crud));\n      },\n\n      async listApiKeys() {\n        const results = Result.orThrow(await app._teamApiKeysCache.getOrWait([session, crud.id], \"write-only\"));\n        return results.map((crud) => app._clientApiKeyFromCrud(session, crud));\n      },\n\n      async createApiKey(options: ApiKeyCreationOptions<\"team\">) {\n        const result = await app._interface.createProjectApiKey(\n          await apiKeyCreationOptionsToCrud(\"team\", crud.id, options),\n          session,\n          \"client\",\n        );\n        await app._teamApiKeysCache.refresh([session, crud.id]);\n        return app._clientApiKeyFromCrud(session, result);\n      },\n    };\n  }\n\n  protected _clientContactChannelFromCrud(crud: ContactChannelsCrud['Client']['Read'], session: InternalSession): ContactChannel {\n    const app = this;\n    return {\n      id: crud.id,\n      value: crud.value,\n      type: crud.type,\n      isVerified: crud.is_verified,\n      isPrimary: crud.is_primary,\n      usedForAuth: crud.used_for_auth,\n\n      async sendVerificationEmail(options?: { callbackUrl?: string }) {\n        await app._interface.sendCurrentUserContactChannelVerificationEmail(\n          crud.id,\n          options?.callbackUrl || constructRedirectUrl(app.urls.emailVerification, \"callbackUrl\"),\n          session\n        );\n      },\n      async update(data: ContactChannelUpdateOptions) {\n        await app._interface.updateClientContactChannel(crud.id, contactChannelUpdateOptionsToCrud(data), session);\n        await app._clientContactChannelsCache.refresh([session]);\n      },\n      async delete() {\n        await app._interface.deleteClientContactChannel(crud.id, session);\n        await app._clientContactChannelsCache.refresh([session]);\n      },\n    };\n  }\n  protected _createAuth(session: InternalSession): Auth {\n    const app = this;\n    return {\n      _internalSession: session,\n      currentSession: {\n        async getTokens() {\n          const tokens = await session.getOrFetchLikelyValidTokens(20_000);\n          return {\n            accessToken: tokens?.accessToken.token ?? null,\n            refreshToken: tokens?.refreshToken?.token ?? null,\n          };\n        },\n      },\n      async getAuthHeaders(): Promise<{ \"x-stack-auth\": string }> {\n        return {\n          \"x-stack-auth\": JSON.stringify(await this.getAuthJson()),\n        };\n      },\n      async getAuthJson(): Promise<{ accessToken: string | null, refreshToken: string | null }> {\n        const tokens = await this.currentSession.getTokens();\n        return tokens;\n      },\n      async registerPasskey(options?: { hostname?: string }): Promise<Result<undefined, KnownErrors[\"PasskeyRegistrationFailed\"] | KnownErrors[\"PasskeyWebAuthnError\"]>> {\n        const hostname = (await app._getCurrentUrl())?.hostname;\n        if (!hostname) {\n          throw new StackAssertionError(\"hostname must be provided if the Stack App does not have a redirect method\");\n        }\n\n        const initiationResult = await app._interface.initiatePasskeyRegistration({}, session);\n\n        if (initiationResult.status !== \"ok\") {\n          return Result.error(new KnownErrors.PasskeyRegistrationFailed(\"Failed to get initiation options for passkey registration\"));\n        }\n\n        const { options_json, code } = initiationResult.data;\n\n        // HACK: Override the rpID to be the actual domain\n        if (options_json.rp.id !== \"THIS_VALUE_WILL_BE_REPLACED.example.com\") {\n          throw new StackAssertionError(`Expected returned RP ID from server to equal sentinel, but found ${options_json.rp.id}`);\n        }\n\n        options_json.rp.id = hostname;\n\n        let attResp;\n        try {\n          attResp = await startRegistration({ optionsJSON: options_json });\n        } catch (error: any) {\n          if (error instanceof WebAuthnError) {\n            return Result.error(new KnownErrors.PasskeyWebAuthnError(error.message, error.name));\n          } else {\n            // This should never happen\n            captureError(\"passkey-registration-failed\", error);\n            return Result.error(new KnownErrors.PasskeyRegistrationFailed(\"Failed to start passkey registration due to unknown error\"));\n          }\n        }\n\n\n        const registrationResult = await app._interface.registerPasskey({ credential: attResp, code }, session);\n\n        await app._refreshUser(session);\n        return registrationResult;\n      },\n      signOut(options?: { redirectUrl?: URL | string }) {\n        return app._signOut(session, options);\n      },\n    };\n  }\n\n  protected _editableTeamProfileFromCrud(crud: TeamMemberProfilesCrud['Client']['Read'], session: InternalSession): EditableTeamMemberProfile {\n    const app = this;\n    return {\n      displayName: crud.display_name,\n      profileImageUrl: crud.profile_image_url,\n      async update(update: { displayName?: string, profileImageUrl?: string }) {\n        await app._interface.updateTeamMemberProfile({\n          teamId: crud.team_id,\n          userId: crud.user_id,\n          profile: {\n            display_name: update.displayName,\n            profile_image_url: update.profileImageUrl,\n          },\n        }, session);\n        await app._currentUserTeamProfileCache.refresh([session, crud.team_id]);\n      }\n    };\n  }\n\n  protected _createBaseUser(crud: NonNullable<CurrentUserCrud['Client']['Read']> | UsersCrud['Server']['Read']): BaseUser {\n    return {\n      id: crud.id,\n      displayName: crud.display_name,\n      primaryEmail: crud.primary_email,\n      primaryEmailVerified: crud.primary_email_verified,\n      profileImageUrl: crud.profile_image_url,\n      signedUpAt: new Date(crud.signed_up_at_millis),\n      clientMetadata: crud.client_metadata,\n      clientReadOnlyMetadata: crud.client_read_only_metadata,\n      hasPassword: crud.has_password,\n      emailAuthEnabled: crud.auth_with_email,\n      otpAuthEnabled: crud.otp_auth_enabled,\n      oauthProviders: crud.oauth_providers,\n      passkeyAuthEnabled: crud.passkey_auth_enabled,\n      isMultiFactorRequired: crud.requires_totp_mfa,\n      isAnonymous: crud.is_anonymous,\n      toClientJson(): CurrentUserCrud['Client']['Read'] {\n        return crud;\n      }\n    };\n  }\n\n  protected _createUserExtraFromCurrent(crud: NonNullable<CurrentUserCrud['Client']['Read']>, session: InternalSession): UserExtra {\n    const app = this;\n    async function getConnectedAccount(id: ProviderType, options?: { scopes?: string[] }): Promise<OAuthConnection | null>;\n    async function getConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): Promise<OAuthConnection>;\n    async function getConnectedAccount(id: ProviderType, options?: { or?: 'redirect', scopes?: string[] }): Promise<OAuthConnection | null> {\n      const scopeString = options?.scopes?.join(\" \");\n      return Result.orThrow(await app._currentUserOAuthConnectionCache.getOrWait([session, id, scopeString || \"\", options?.or === 'redirect'], \"write-only\"));\n    }\n\n    function useConnectedAccount(id: ProviderType, options?: { scopes?: string[] }): OAuthConnection | null;\n    function useConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): OAuthConnection;\n    function useConnectedAccount(id: ProviderType, options?: { or?: 'redirect', scopes?: string[] }): OAuthConnection | null {\n      const scopeString = options?.scopes?.join(\" \");\n      return useAsyncCache(app._currentUserOAuthConnectionCache, [session, id, scopeString || \"\", options?.or === 'redirect'] as const, \"user.useConnectedAccount()\");\n    }\n    return {\n      async getActiveSessions() {\n        const sessions = await app._interface.listSessions(session);\n        return sessions.items.map((crud) => app._clientSessionFromCrud(crud));\n      },\n      async revokeSession(sessionId: string) {\n        await app._interface.deleteSession(sessionId, session);\n      },\n      setDisplayName(displayName: string) {\n        return this.update({ displayName });\n      },\n      setClientMetadata(metadata: Record<string, any>) {\n        return this.update({ clientMetadata: metadata });\n      },\n      async setSelectedTeam(team: Team | null) {\n        await this.update({ selectedTeamId: team?.id ?? null });\n      },\n      getConnectedAccount,\n      useConnectedAccount, // THIS_LINE_PLATFORM react-like\n      async getTeam(teamId: string) {\n        const teams = await this.listTeams();\n        return teams.find((t) => t.id === teamId) ?? null;\n      },\n      useTeam(teamId: string) {\n        const teams = this.useTeams();\n        return useMemo(() => {\n          return teams.find((t) => t.id === teamId) ?? null;\n        }, [teams, teamId]);\n      },\n      async listTeams() {\n        const teams = Result.orThrow(await app._currentUserTeamsCache.getOrWait([session], \"write-only\"));\n        return teams.map((crud) => app._clientTeamFromCrud(crud, session));\n      },\n      useTeams() {\n        const teams = useAsyncCache(app._currentUserTeamsCache, [session], \"user.useTeams()\");\n        return useMemo(() => teams.map((crud) => app._clientTeamFromCrud(crud, session)), [teams]);\n      },\n      async createTeam(data: TeamCreateOptions) {\n        const crud = await app._interface.createClientTeam(teamCreateOptionsToCrud(data, 'me'), session);\n        await app._currentUserTeamsCache.refresh([session]);\n        await this.update({ selectedTeamId: crud.id });\n        return app._clientTeamFromCrud(crud, session);\n      },\n      async leaveTeam(team: Team) {\n        await app._interface.leaveTeam(team.id, session);\n        // TODO: refresh cache\n      },\n      async listPermissions(scopeOrOptions?: Team | { recursive?: boolean }, options?: { recursive?: boolean }): Promise<TeamPermission[]> {\n        if (scopeOrOptions && 'id' in scopeOrOptions) {\n          const scope = scopeOrOptions;\n          const recursive = options?.recursive ?? true;\n          const permissions = Result.orThrow(await app._currentUserPermissionsCache.getOrWait([session, scope.id, recursive], \"write-only\"));\n          return permissions.map((crud) => app._clientPermissionFromCrud(crud));\n        } else {\n          const opts = scopeOrOptions;\n          const recursive = opts?.recursive ?? true;\n          const permissions = Result.orThrow(await app._currentUserProjectPermissionsCache.getOrWait([session, recursive], \"write-only\"));\n          return permissions.map((crud) => app._clientPermissionFromCrud(crud));\n        }\n      },\n      usePermissions(scopeOrOptions?: Team | { recursive?: boolean }, options?: { recursive?: boolean }): TeamPermission[] {\n        if (scopeOrOptions && 'id' in scopeOrOptions) {\n          const scope = scopeOrOptions;\n          const recursive = options?.recursive ?? true;\n          const permissions = useAsyncCache(app._currentUserPermissionsCache, [session, scope.id, recursive] as const, \"user.usePermissions()\");\n          return useMemo(() => permissions.map((crud) => app._clientPermissionFromCrud(crud)), [permissions]);\n        } else {\n          const opts = scopeOrOptions;\n          const recursive = opts?.recursive ?? true;\n          const permissions = useAsyncCache(app._currentUserProjectPermissionsCache, [session, recursive] as const, \"user.usePermissions()\");\n          return useMemo(() => permissions.map((crud) => app._clientPermissionFromCrud(crud)), [permissions]);\n        }\n      },\n      usePermission(scopeOrPermissionId: Team | string, permissionId?: string): TeamPermission | null {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          const permissions = this.usePermissions(scope);\n          return useMemo(() => permissions.find((p) => p.id === permissionId) ?? null, [permissions, permissionId]);\n        } else {\n          const pid = scopeOrPermissionId;\n          const permissions = this.usePermissions();\n          return useMemo(() => permissions.find((p) => p.id === pid) ?? null, [permissions, pid]);\n        }\n      },\n      async getPermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<TeamPermission | null> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          const permissions = await this.listPermissions(scope);\n          return permissions.find((p) => p.id === permissionId) ?? null;\n        } else {\n          const pid = scopeOrPermissionId;\n          const permissions = await this.listPermissions();\n          return permissions.find((p) => p.id === pid) ?? null;\n        }\n      },\n      async hasPermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<boolean> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          return (await this.getPermission(scope, permissionId as string)) !== null;\n        } else {\n          const pid = scopeOrPermissionId;\n          return (await this.getPermission(pid)) !== null;\n        }\n      },\n      async update(update) {\n        return await app._updateClientUser(update, session);\n      },\n      async sendVerificationEmail(options?: { callbackUrl?: string }) {\n        if (!crud.primary_email) {\n          throw new StackAssertionError(\"User does not have a primary email\");\n        }\n        return await app._interface.sendVerificationEmail(\n          crud.primary_email,\n          options?.callbackUrl ?? constructRedirectUrl(app.urls.emailVerification, \"callbackUrl\"),\n          session\n        );\n      },\n      async updatePassword(options: { oldPassword: string, newPassword: string}) {\n        const result = await app._interface.updatePassword(options, session);\n        await app._currentUserCache.refresh([session]);\n        return result;\n      },\n      async setPassword(options: { password: string }) {\n        const result = await app._interface.setPassword(options, session);\n        await app._currentUserCache.refresh([session]);\n        return result;\n      },\n      selectedTeam: crud.selected_team && this._clientTeamFromCrud(crud.selected_team, session),\n      async getTeamProfile(team: Team) {\n        const result = Result.orThrow(await app._currentUserTeamProfileCache.getOrWait([session, team.id], \"write-only\"));\n        return app._editableTeamProfileFromCrud(result, session);\n      },\n      useTeamProfile(team: Team) {\n        const result = useAsyncCache(app._currentUserTeamProfileCache, [session, team.id] as const, \"user.useTeamProfile()\");\n        return app._editableTeamProfileFromCrud(result, session);\n      },\n      async delete() {\n        await app._interface.deleteCurrentUser(session);\n        session.markInvalid();\n      },\n      async listContactChannels() {\n        const result = Result.orThrow(await app._clientContactChannelsCache.getOrWait([session], \"write-only\"));\n        return result.map((crud) => app._clientContactChannelFromCrud(crud, session));\n      },\n      useContactChannels() {\n        const result = useAsyncCache(app._clientContactChannelsCache, [session] as const, \"user.useContactChannels()\");\n        return result.map((crud) => app._clientContactChannelFromCrud(crud, session));\n      },\n      async createContactChannel(data: ContactChannelCreateOptions) {\n        const crud = await app._interface.createClientContactChannel(contactChannelCreateOptionsToCrud('me', data), session);\n        await app._clientContactChannelsCache.refresh([session]);\n        return app._clientContactChannelFromCrud(crud, session);\n      },\n\n      useApiKeys() {\n        const result = useAsyncCache(app._userApiKeysCache, [session] as const, \"user.useApiKeys()\");\n        return result.map((crud) => app._clientApiKeyFromCrud(session, crud));\n      },\n\n      async listApiKeys() {\n        const results = await app._interface.listProjectApiKeys({ user_id: 'me' }, session, \"client\");\n        return results.map((crud) => app._clientApiKeyFromCrud(session, crud));\n      },\n\n      async createApiKey(options: ApiKeyCreationOptions<\"user\">) {\n        const result = await app._interface.createProjectApiKey(\n          await apiKeyCreationOptionsToCrud(\"user\", \"me\", options),\n          session,\n          \"client\",\n        );\n        await app._userApiKeysCache.refresh([session]);\n        return app._clientApiKeyFromCrud(session, result);\n      },\n    };\n  }\n\n  protected _createInternalUserExtra(session: InternalSession): InternalUserExtra {\n    const app = this;\n    this._ensureInternalProject();\n    return {\n      createProject(newProject: AdminProjectUpdateOptions & { displayName: string }) {\n        return app._createProject(session, newProject);\n      },\n      listOwnedProjects() {\n        return app._listOwnedProjects(session);\n      },\n      useOwnedProjects() {\n        return app._useOwnedProjects(session);\n      },\n    };\n  }\n\n  protected _currentUserFromCrud(crud: NonNullable<CurrentUserCrud['Client']['Read']>, session: InternalSession): ProjectCurrentUser<ProjectId> {\n    const currentUser = {\n      ...this._createBaseUser(crud),\n      ...this._createAuth(session),\n      ...this._createUserExtraFromCurrent(crud, session),\n      ...this._isInternalProject() ? this._createInternalUserExtra(session) : {},\n    } satisfies CurrentUser;\n\n    Object.freeze(currentUser);\n    return currentUser as ProjectCurrentUser<ProjectId>;\n  }\n  protected _clientSessionFromCrud(crud: SessionsCrud['Client']['Read']): ActiveSession {\n    return {\n      id: crud.id,\n      userId: crud.user_id,\n      createdAt: new Date(crud.created_at),\n      isImpersonation: crud.is_impersonation,\n      lastUsedAt: crud.last_used_at ? new Date(crud.last_used_at) : undefined,\n      isCurrentSession: crud.is_current_session ?? false,\n      geoInfo: crud.last_used_at_end_user_ip_info,\n    };\n  }\n\n  protected _getOwnedAdminApp(forProjectId: string, session: InternalSession): _StackAdminAppImplIncomplete<false, string> {\n    if (!this._ownedAdminApps.has([session, forProjectId])) {\n      this._ownedAdminApps.set([session, forProjectId], new (_StackClientAppImplIncomplete.LazyStackAdminAppImpl.value!)({\n        baseUrl: this._interface.options.getBaseUrl(),\n        projectId: forProjectId,\n        tokenStore: null,\n        projectOwnerSession: session,\n        noAutomaticPrefetch: true,\n      }));\n    }\n    return this._ownedAdminApps.get([session, forProjectId])!;\n  }\n\n  get projectId(): ProjectId {\n    return this._interface.projectId as ProjectId;\n  }\n\n  protected async _isTrusted(url: string): Promise<boolean> {\n    return isRelative(url);\n  }\n\n  get urls(): Readonly<HandlerUrls> {\n    return getUrls(this._urlOptions);\n  }\n\n  protected async _getCurrentUrl() {\n    if (this._redirectMethod === \"none\") {\n      return null;\n    }\n    return new URL(window.location.href);\n  }\n\n  protected async _redirectTo(options: { url: URL | string, replace?: boolean }) {\n    if (this._redirectMethod === \"none\") {\n      return;\n    } else if (typeof this._redirectMethod === \"object\" && this._redirectMethod.navigate) {\n      this._redirectMethod.navigate(options.url.toString());\n    } else {\n      if (options.replace) {\n        window.location.replace(options.url);\n      } else {\n        window.location.assign(options.url);\n      }\n    }\n\n    await wait(2000);\n  }\n\n  useNavigate(): (to: string) => void {\n    if (typeof this._redirectMethod === \"object\") {\n      return this._redirectMethod.useNavigate();\n    } else if (this._redirectMethod === \"window\") {\n      return (to: string) => window.location.assign(to);\n    } else {\n      return (to: string) => {};\n    }\n  }\n  protected async _redirectIfTrusted(url: string, options?: RedirectToOptions) {\n    if (!await this._isTrusted(url)) {\n      throw new Error(`Redirect URL ${url} is not trusted; should be relative.`);\n    }\n    return await this._redirectTo({ url, ...options });\n  }\n\n  protected async _redirectToHandler(handlerName: keyof HandlerUrls, options?: RedirectToOptions) {\n    let url = this.urls[handlerName];\n    if (!url) {\n      throw new Error(`No URL for handler name ${handlerName}`);\n    }\n\n    if (!options?.noRedirectBack) {\n      if (handlerName === \"afterSignIn\" || handlerName === \"afterSignUp\") {\n        if (isReactServer || typeof window === \"undefined\") {\n          // TODO implement this\n        } else {\n          const queryParams = new URLSearchParams(window.location.search);\n          url = queryParams.get(\"after_auth_return_to\") || url;\n        }\n      } else if (handlerName === \"signIn\" || handlerName === \"signUp\") {\n        if (isReactServer || typeof window === \"undefined\") {\n          // TODO implement this\n        } else {\n          const currentUrl = new URL(window.location.href);\n          const nextUrl = new URL(url, currentUrl);\n          if (currentUrl.searchParams.has(\"after_auth_return_to\")) {\n            nextUrl.searchParams.set(\"after_auth_return_to\", currentUrl.searchParams.get(\"after_auth_return_to\")!);\n          } else if (currentUrl.protocol === nextUrl.protocol && currentUrl.host === nextUrl.host) {\n            nextUrl.searchParams.set(\"after_auth_return_to\", getRelativePart(currentUrl));\n          }\n          url = getRelativePart(nextUrl);\n        }\n      }\n    }\n\n    await this._redirectIfTrusted(url, options);\n  }\n\n  async redirectToSignIn(options?: RedirectToOptions) { return await this._redirectToHandler(\"signIn\", options); }\n  async redirectToSignUp(options?: RedirectToOptions) { return await this._redirectToHandler(\"signUp\", options); }\n  async redirectToSignOut(options?: RedirectToOptions) { return await this._redirectToHandler(\"signOut\", options); }\n  async redirectToEmailVerification(options?: RedirectToOptions) { return await this._redirectToHandler(\"emailVerification\", options); }\n  async redirectToPasswordReset(options?: RedirectToOptions) { return await this._redirectToHandler(\"passwordReset\", options); }\n  async redirectToForgotPassword(options?: RedirectToOptions) { return await this._redirectToHandler(\"forgotPassword\", options); }\n  async redirectToHome(options?: RedirectToOptions) { return await this._redirectToHandler(\"home\", options); }\n  async redirectToOAuthCallback(options?: RedirectToOptions) { return await this._redirectToHandler(\"oauthCallback\", options); }\n  async redirectToMagicLinkCallback(options?: RedirectToOptions) { return await this._redirectToHandler(\"magicLinkCallback\", options); }\n  async redirectToAfterSignIn(options?: RedirectToOptions) { return await this._redirectToHandler(\"afterSignIn\", options); }\n  async redirectToAfterSignUp(options?: RedirectToOptions) { return await this._redirectToHandler(\"afterSignUp\", options); }\n  async redirectToAfterSignOut(options?: RedirectToOptions) { return await this._redirectToHandler(\"afterSignOut\", options); }\n  async redirectToAccountSettings(options?: RedirectToOptions) { return await this._redirectToHandler(\"accountSettings\", options); }\n  async redirectToError(options?: RedirectToOptions) { return await this._redirectToHandler(\"error\", options); }\n  async redirectToTeamInvitation(options?: RedirectToOptions) { return await this._redirectToHandler(\"teamInvitation\", options); }\n\n  async sendForgotPasswordEmail(email: string, options?: { callbackUrl?: string }): Promise<Result<undefined, KnownErrors[\"UserNotFound\"]>> {\n    return await this._interface.sendForgotPasswordEmail(email, options?.callbackUrl ?? constructRedirectUrl(this.urls.passwordReset, \"callbackUrl\"));\n  }\n\n  async sendMagicLinkEmail(email: string, options?: { callbackUrl?: string }): Promise<Result<{ nonce: string }, KnownErrors[\"RedirectUrlNotWhitelisted\"]>> {\n    return await this._interface.sendMagicLinkEmail(email, options?.callbackUrl ?? constructRedirectUrl(this.urls.magicLinkCallback, \"callbackUrl\"));\n  }\n\n  async resetPassword(options: { password: string, code: string }): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    return await this._interface.resetPassword(options);\n  }\n\n  async verifyPasswordResetCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    return await this._interface.verifyPasswordResetCode(code);\n  }\n\n  async verifyTeamInvitationCode(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    return await this._interface.acceptTeamInvitation({\n      type: 'check',\n      code,\n      session: await this._getSession(),\n    });\n  }\n\n  async acceptTeamInvitation(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const result = await this._interface.acceptTeamInvitation({\n      type: 'use',\n      code,\n      session: await this._getSession(),\n    });\n\n    if (result.status === 'ok') {\n      return Result.ok(undefined);\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n  async getTeamInvitationDetails(code: string): Promise<Result<{ teamDisplayName: string }, KnownErrors[\"VerificationCodeError\"]>> {\n    const result = await this._interface.acceptTeamInvitation({\n      type: 'details',\n      code,\n      session: await this._getSession(),\n    });\n\n    if (result.status === 'ok') {\n      return Result.ok({ teamDisplayName: result.data.team_display_name });\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n  async verifyEmail(code: string): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"]>> {\n    const result = await this._interface.verifyEmail(code);\n    await this._currentUserCache.refresh([await this._getSession()]);\n    await this._clientContactChannelsCache.refresh([await this._getSession()]);\n    return result;\n  }\n\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): Promise<ProjectCurrentUser<ProjectId>>;\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): Promise<ProjectCurrentUser<ProjectId>>;\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): Promise<ProjectCurrentUser<ProjectId>>;\n  async getUser(options?: GetUserOptions<HasTokenStore>): Promise<ProjectCurrentUser<ProjectId> | null>;\n  async getUser(options?: GetUserOptions<HasTokenStore>): Promise<ProjectCurrentUser<ProjectId> | null> {\n    this._ensurePersistentTokenStore(options?.tokenStore);\n    const session = await this._getSession(options?.tokenStore);\n    let crud = Result.orThrow(await this._currentUserCache.getOrWait([session], \"write-only\"));\n    if (crud?.is_anonymous && options?.or !== \"anonymous\" && options?.or !== \"anonymous-if-exists\") {\n      crud = null;\n    }\n\n    if (crud === null) {\n      switch (options?.or) {\n        case 'redirect': {\n          await this.redirectToSignIn({ replace: true });\n          break;\n        }\n        case 'throw': {\n          throw new Error(\"User is not signed in but getUser was called with { or: 'throw' }\");\n        }\n        case 'anonymous': {\n          const tokens = await this._signUpAnonymously();\n          return await this.getUser({ tokenStore: tokens, or: \"anonymous-if-exists\" }) ?? throwErr(\"Something went wrong while signing up anonymously\");\n        }\n        case undefined:\n        case \"anonymous-if-exists\":\n        case \"return-null\": {\n          return null;\n        }\n      }\n    }\n\n    return crud && this._currentUserFromCrud(crud, session);\n  }\n\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): ProjectCurrentUser<ProjectId>;\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): ProjectCurrentUser<ProjectId>;\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): ProjectCurrentUser<ProjectId>;\n  useUser(options?: GetUserOptions<HasTokenStore>): ProjectCurrentUser<ProjectId> | null;\n  useUser(options?: GetUserOptions<HasTokenStore>): ProjectCurrentUser<ProjectId> | null {\n    this._ensurePersistentTokenStore(options?.tokenStore);\n\n    const session = this._useSession(options?.tokenStore);\n    let crud = useAsyncCache(this._currentUserCache, [session] as const, \"useUser()\");\n    if (crud?.is_anonymous && options?.or !== \"anonymous\" && options?.or !== \"anonymous-if-exists\") {\n      crud = null;\n    }\n\n    if (crud === null) {\n      switch (options?.or) {\n        case 'redirect': {\n          runAsynchronously(this.redirectToSignIn({ replace: true }));\n          suspend();\n          throw new StackAssertionError(\"suspend should never return\");\n        }\n        case 'throw': {\n          throw new Error(\"User is not signed in but useUser was called with { or: 'throw' }\");\n        }\n        case 'anonymous': {\n          // TODO we should think about the behavior when calling useUser (or getUser) in anonymous with a custom token store. signUpAnonymously always sets the current token store on app level, instead of the one passed to this function\n          // TODO we shouldn't reload & suspend here, instead we should use a promise that resolves to the new anonymous user\n          runAsynchronously(async () => {\n            await this._signUpAnonymously();\n            if (typeof window !== \"undefined\") {\n              window.location.reload();\n            }\n          });\n          suspend();\n          throw new StackAssertionError(\"suspend should never return\");\n        }\n        case undefined:\n        case \"anonymous-if-exists\":\n        case \"return-null\": {\n          // do nothing\n        }\n      }\n    }\n\n    return useMemo(() => {\n      return crud && this._currentUserFromCrud(crud, session);\n    }, [crud, session, options?.or]);\n  }\n\n  protected async _updateClientUser(update: UserUpdateOptions, session: InternalSession) {\n    const res = await this._interface.updateClientUser(userUpdateOptionsToCrud(update), session);\n    await this._refreshUser(session);\n    return res;\n  }\n\n  async signInWithOAuth(provider: ProviderType) {\n    if (typeof window === \"undefined\") {\n      throw new Error(\"signInWithOAuth can currently only be called in a browser environment\");\n    }\n\n    this._ensurePersistentTokenStore();\n    await signInWithOAuth(\n      this._interface, {\n        provider,\n        redirectUrl: this.urls.oauthCallback,\n        errorRedirectUrl: this.urls.error,\n        providerScope: this._oauthScopesOnSignIn[provider]?.join(\" \"),\n      }\n    );\n  }\n\n  /**\n   * @deprecated\n   * TODO remove\n   */\n  protected async _experimentalMfa(error: KnownErrors['MultiFactorAuthenticationRequired'], session: InternalSession) {\n    const otp = prompt('Please enter the six-digit TOTP code from your authenticator app.');\n    if (!otp) {\n      throw new KnownErrors.InvalidTotpCode();\n    }\n\n    return await this._interface.totpMfa(\n      (error.details as any)?.attempt_code ?? throwErr(\"attempt code missing\"),\n      otp,\n      session\n    );\n  }\n\n  /**\n   * @deprecated\n   * TODO remove\n   */\n  protected async _catchMfaRequiredError<T, E>(callback: () => Promise<Result<T, E>>): Promise<Result<T | { accessToken: string, refreshToken: string, newUser: boolean }, E>> {\n    try {\n      return await callback();\n    } catch (e) {\n      if (KnownErrors.MultiFactorAuthenticationRequired.isInstance(e)) {\n        return Result.ok(await this._experimentalMfa(e, await this._getSession()));\n      }\n      throw e;\n    }\n  }\n\n  async signInWithCredential(options: {\n    email: string,\n    password: string,\n    noRedirect?: boolean,\n  }): Promise<Result<undefined, KnownErrors[\"EmailPasswordMismatch\"] | KnownErrors[\"InvalidTotpCode\"]>> {\n    this._ensurePersistentTokenStore();\n    const session = await this._getSession();\n    let result;\n    try {\n      result = await this._catchMfaRequiredError(async () => {\n        return await this._interface.signInWithCredential(options.email, options.password, session);\n      });\n    } catch (e) {\n      if (KnownErrors.InvalidTotpCode.isInstance(e)) {\n        return Result.error(e);\n      }\n      throw e;\n    }\n\n    if (result.status === 'ok') {\n      await this._signInToAccountWithTokens(result.data);\n      if (!options.noRedirect) {\n        await this.redirectToAfterSignIn({ replace: true });\n      }\n      return Result.ok(undefined);\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n  async signUpWithCredential(options: {\n    email: string,\n    password: string,\n    noRedirect?: boolean,\n    verificationCallbackUrl?: string,\n  }): Promise<Result<undefined, KnownErrors[\"UserWithEmailAlreadyExists\"] | KnownErrors['PasswordRequirementsNotMet']>> {\n    this._ensurePersistentTokenStore();\n    const session = await this._getSession();\n    const emailVerificationRedirectUrl = options.verificationCallbackUrl ?? constructRedirectUrl(this.urls.emailVerification, \"verificationCallbackUrl\");\n    const result = await this._interface.signUpWithCredential(\n      options.email,\n      options.password,\n      emailVerificationRedirectUrl,\n      session\n    );\n    if (result.status === 'ok') {\n      await this._signInToAccountWithTokens(result.data);\n      if (!options.noRedirect) {\n        await this.redirectToAfterSignUp({ replace: true });\n      }\n      return Result.ok(undefined);\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n  async _signUpAnonymously() {\n    this._ensurePersistentTokenStore();\n\n    if (!this._anonymousSignUpInProgress) {\n      this._anonymousSignUpInProgress = (async () => {\n        this._ensurePersistentTokenStore();\n        const session = await this._getSession();\n        const result = await this._interface.signUpAnonymously(session);\n        if (result.status === \"ok\") {\n          await this._signInToAccountWithTokens(result.data);\n        } else {\n          throw new StackAssertionError(\"signUpAnonymously() should never return an error\");\n        }\n        this._anonymousSignUpInProgress = null;\n        return result.data;\n      })();\n    }\n\n    return await this._anonymousSignUpInProgress;\n  }\n\n  async signInWithMagicLink(code: string, options?: { noRedirect?: boolean }): Promise<Result<undefined, KnownErrors[\"VerificationCodeError\"] | KnownErrors[\"InvalidTotpCode\"]>> {\n    this._ensurePersistentTokenStore();\n    let result;\n    try {\n      result = await this._catchMfaRequiredError(async () => {\n        return await this._interface.signInWithMagicLink(code);\n      });\n    } catch (e) {\n      if (KnownErrors.InvalidTotpCode.isInstance(e)) {\n        return Result.error(e);\n      }\n      throw e;\n    }\n\n    if (result.status === 'ok') {\n      await this._signInToAccountWithTokens(result.data);\n      if (!(options?.noRedirect)) {\n        if (result.data.newUser) {\n          await this.redirectToAfterSignUp({ replace: true });\n        } else {\n          await this.redirectToAfterSignIn({ replace: true });\n        }\n      }\n      return Result.ok(undefined);\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n  /**\n   * Initiates a CLI authentication process that allows a command line application\n   * to get a refresh token for a user's account.\n   *\n   * This process works as follows:\n   * 1. The CLI app calls this method, which initiates the auth process with the server\n   * 2. The server returns a polling code and a login code\n   * 3. The CLI app opens a browser window to the appUrl with the login code as a parameter\n   * 4. The user logs in through the browser and confirms the authorization\n   * 5. The CLI app polls for the refresh token using the polling code\n   *\n   * @param options Options for the CLI login\n   * @param options.appUrl The URL of the app that will handle the CLI auth confirmation\n   * @param options.expiresInMillis Optional duration in milliseconds before the auth attempt expires (default: 2 hours)\n   * @param options.maxAttempts Optional maximum number of polling attempts (default: Infinity)\n   * @param options.waitTimeMillis Optional time to wait between polling attempts (default: 2 seconds)\n   * @param options.promptLink Optional function to call with the login URL to prompt the user to open the browser\n   * @returns Result containing either the refresh token or an error\n   */\n  async promptCliLogin(options: {\n    appUrl: string,\n    expiresInMillis?: number,\n    maxAttempts?: number,\n    waitTimeMillis?: number,\n    promptLink?: (url: string) => void,\n  }): Promise<Result<string, KnownErrors[\"CliAuthError\"] | KnownErrors[\"CliAuthExpiredError\"] | KnownErrors[\"CliAuthUsedError\"]>> {\n    // Step 1: Initiate the CLI auth process\n    const response = await this._interface.sendClientRequest(\n      \"/auth/cli\",\n      {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          expires_in_millis: options.expiresInMillis,\n        }),\n      },\n      null\n    );\n\n    if (!response.ok) {\n      return Result.error(new KnownErrors.CliAuthError(`Failed to initiate CLI auth: ${response.status} ${await response.text()}`));\n    }\n\n    const initResult = await response.json();\n    const pollingCode = initResult.polling_code;\n    const loginCode = initResult.login_code;\n\n    // Step 2: Open the browser for the user to authenticate\n    const url = `${options.appUrl}/handler/cli-auth-confirm?login_code=${encodeURIComponent(loginCode)}`;\n    if (options.promptLink) {\n      options.promptLink(url);\n    } else {\n      console.log(`Please visit the following URL to authenticate:\\n${url}`);\n    }\n\n\n    // Step 3: Poll for the token\n    let attempts = 0;\n    while (attempts < (options.maxAttempts ?? Infinity)) {\n      attempts++;\n      const pollResponse = await this._interface.sendClientRequest(\"/auth/cli/poll\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          polling_code: pollingCode,\n        }),\n      }, null);\n\n      if (!pollResponse.ok) {\n        return Result.error(new KnownErrors.CliAuthError(`Failed to initiate CLI auth: ${pollResponse.status} ${await pollResponse.text()}`));\n      }\n      const pollResult = await pollResponse.json();\n\n      if (pollResponse.status === 201 && pollResult.status === \"success\") {\n        return Result.ok(pollResult.refresh_token);\n      }\n      if (pollResult.status === \"waiting\") {\n        await wait(options.waitTimeMillis ?? 2000);\n        continue;\n      }\n      if (pollResult.status === \"expired\") {\n        return Result.error(new KnownErrors.CliAuthExpiredError(\"CLI authentication request expired. Please try again.\"));\n      }\n      if (pollResult.status === \"used\") {\n        return Result.error(new KnownErrors.CliAuthUsedError(\"This authentication token has already been used.\"));\n      }\n      return Result.error(new KnownErrors.CliAuthError(`Unexpected status from CLI auth polling: ${pollResult.status}`));\n    }\n\n    return Result.error(new KnownErrors.CliAuthError(\"Timed out waiting for CLI authentication.\"));\n  }\n\n  async signInWithPasskey(): Promise<Result<undefined, KnownErrors[\"PasskeyAuthenticationFailed\"] | KnownErrors[\"InvalidTotpCode\"] | KnownErrors[\"PasskeyWebAuthnError\"]>> {\n    this._ensurePersistentTokenStore();\n    const session = await this._getSession();\n    let result;\n    try {\n      result = await this._catchMfaRequiredError(async () => {\n        const initiationResult = await this._interface.initiatePasskeyAuthentication({}, session);\n        if (initiationResult.status !== \"ok\") {\n          return Result.error(new KnownErrors.PasskeyAuthenticationFailed(\"Failed to get initiation options for passkey authentication\"));\n        }\n\n        const { options_json, code } = initiationResult.data;\n\n        // HACK: Override the rpID to be the actual domain\n        if (options_json.rpId !== \"THIS_VALUE_WILL_BE_REPLACED.example.com\") {\n          throw new StackAssertionError(`Expected returned RP ID from server to equal sentinel, but found ${options_json.rpId}`);\n        }\n        options_json.rpId = window.location.hostname;\n\n        const authentication_response = await startAuthentication({ optionsJSON: options_json });\n        return await this._interface.signInWithPasskey({ authentication_response, code });\n      });\n    } catch (error) {\n      if (error instanceof WebAuthnError) {\n        return Result.error(new KnownErrors.PasskeyWebAuthnError(error.message, error.name));\n      } else {\n        // This should never happen\n        return Result.error(new KnownErrors.PasskeyAuthenticationFailed(\"Failed to sign in with passkey\"));\n      }\n    }\n\n    if (result.status === 'ok') {\n      await this._signInToAccountWithTokens(result.data);\n      await this.redirectToAfterSignIn({ replace: true });\n      return Result.ok(undefined);\n    } else {\n      return Result.error(result.error);\n    }\n  }\n\n\n  async callOAuthCallback() {\n    if (typeof window === \"undefined\") {\n      throw new Error(\"callOAuthCallback can currently only be called in a browser environment\");\n    }\n    this._ensurePersistentTokenStore();\n    let result;\n    try {\n      result = await this._catchMfaRequiredError(async () => {\n        return await callOAuthCallback(this._interface, this.urls.oauthCallback);\n      });\n    } catch (e) {\n      if (KnownErrors.InvalidTotpCode.isInstance(e)) {\n        alert(\"Invalid TOTP code. Please try signing in again.\");\n        return false;\n      } else {\n        throw e;\n      }\n    }\n    if (result.status === 'ok' && result.data) {\n      await this._signInToAccountWithTokens(result.data);\n      // TODO fix afterCallbackRedirectUrl for MFA (currently not passed because /mfa/sign-in doesn't return it)\n      // or just get rid of afterCallbackRedirectUrl entirely tbh\n      if (\"afterCallbackRedirectUrl\" in result.data && result.data.afterCallbackRedirectUrl) {\n        await this._redirectTo({ url: result.data.afterCallbackRedirectUrl, replace: true });\n        return true;\n      } else if (result.data.newUser) {\n        await this.redirectToAfterSignUp({ replace: true });\n        return true;\n      } else {\n        await this.redirectToAfterSignIn({ replace: true });\n        return true;\n      }\n    }\n    return false;\n  }\n\n  protected async _signOut(session: InternalSession, options?: { redirectUrl?: URL | string }): Promise<void> {\n    await storeLock.withWriteLock(async () => {\n      await this._interface.signOut(session);\n      if (options?.redirectUrl) {\n        await this._redirectTo({ url: options.redirectUrl, replace: true });\n      } else {\n        await this.redirectToAfterSignOut();\n      }\n    });\n  }\n\n  async signOut(options?: { redirectUrl?: URL | string }): Promise<void> {\n    const user = await this.getUser();\n    if (user) {\n      await user.signOut(options);\n    }\n  }\n\n  async getProject(): Promise<Project> {\n    const crud = Result.orThrow(await this._currentProjectCache.getOrWait([], \"write-only\"));\n    return this._clientProjectFromCrud(crud);\n  }\n\n  useProject(): Project {\n    const crud = useAsyncCache(this._currentProjectCache, [], \"useProject()\");\n    return useMemo(() => this._clientProjectFromCrud(crud), [crud]);\n  }\n\n  protected async _listOwnedProjects(session: InternalSession): Promise<AdminOwnedProject[]> {\n    this._ensureInternalProject();\n    const crud = Result.orThrow(await this._ownedProjectsCache.getOrWait([session], \"write-only\"));\n    return crud.map((j) => this._getOwnedAdminApp(j.id, session)._adminOwnedProjectFromCrud(\n      j,\n      () => this._refreshOwnedProjects(session),\n    ));\n  }\n\n  protected _useOwnedProjects(session: InternalSession): AdminOwnedProject[] {\n    this._ensureInternalProject();\n    const projects = useAsyncCache(this._ownedProjectsCache, [session], \"useOwnedProjects()\");\n    return useMemo(() => projects.map((j) => this._getOwnedAdminApp(j.id, session)._adminOwnedProjectFromCrud(\n      j,\n      () => this._refreshOwnedProjects(session),\n    )), [projects]);\n  }\n  protected async _createProject(session: InternalSession, newProject: AdminProjectUpdateOptions & { displayName: string }): Promise<AdminOwnedProject> {\n    this._ensureInternalProject();\n    const crud = await this._interface.createProject(adminProjectCreateOptionsToCrud(newProject), session);\n    const res = this._getOwnedAdminApp(crud.id, session)._adminOwnedProjectFromCrud(\n      crud,\n      () => this._refreshOwnedProjects(session),\n    );\n    await this._refreshOwnedProjects(session);\n    return res;\n  }\n\n  protected async _refreshUser(session: InternalSession) {\n    // TODO this should take a user ID instead of a session, and automatically refresh all sessions with that user ID\n    await this._refreshSession(session);\n  }\n\n  protected async _refreshSession(session: InternalSession) {\n    await this._currentUserCache.refresh([session]);\n  }\n\n  protected async _refreshUsers() {\n    // nothing yet\n  }\n\n  protected async _refreshProject() {\n    await this._currentProjectCache.refresh([]);\n  }\n\n  protected async _refreshOwnedProjects(session: InternalSession) {\n    await this._ownedProjectsCache.refresh([session]);\n  }\n\n  static get [stackAppInternalsSymbol]() {\n    return {\n      fromClientJson: <HasTokenStore extends boolean, ProjectId extends string>(\n        json: StackClientAppJson<HasTokenStore, ProjectId>\n      ): StackClientApp<HasTokenStore, ProjectId> => {\n        const providedCheckString = JSON.stringify(omit(json, [/* none currently */]));\n        const existing = allClientApps.get(json.uniqueIdentifier);\n        if (existing) {\n          const [existingCheckString, clientApp] = existing;\n          if (existingCheckString !== undefined && existingCheckString !== providedCheckString) {\n            throw new StackAssertionError(\"The provided app JSON does not match the configuration of the existing client app with the same unique identifier\", { providedObj: json, existingString: existingCheckString });\n          }\n          return clientApp as any;\n        }\n\n        return new _StackClientAppImplIncomplete<HasTokenStore, ProjectId>({\n          ...json,\n          checkString: providedCheckString,\n        });\n      }\n    };\n  }\n\n  get [stackAppInternalsSymbol]() {\n    return {\n      toClientJson: (): StackClientAppJson<HasTokenStore, ProjectId> => {\n        if (!(\"publishableClientKey\" in this._interface.options)) {\n          // TODO find a way to do this\n          throw new StackAssertionError(\"Cannot serialize to JSON from an application without a publishable client key\");\n        }\n\n        if (typeof this._redirectMethod !== \"string\") {\n          throw new StackAssertionError(\"Cannot serialize to JSON from an application with a non-string redirect method\");\n        }\n\n        return {\n          baseUrl: this._options.baseUrl,\n          projectId: this.projectId,\n          publishableClientKey: this._interface.options.publishableClientKey,\n          tokenStore: this._tokenStoreInit,\n          urls: this._urlOptions,\n          oauthScopesOnSignIn: this._oauthScopesOnSignIn,\n          uniqueIdentifier: this._getUniqueIdentifier(),\n          redirectMethod: this._redirectMethod,\n          extraRequestHeaders: this._options.extraRequestHeaders,\n        };\n      },\n      setCurrentUser: (userJsonPromise: Promise<CurrentUserCrud['Client']['Read'] | null>) => {\n        runAsynchronously(async () => {\n          await this._currentUserCache.forceSetCachedValueAsync([await this._getSession()], Result.fromPromise(userJsonPromise));\n        });\n      },\n      sendRequest: async (\n        path: string,\n        requestOptions: RequestInit,\n        requestType: \"client\" | \"server\" | \"admin\" = \"client\",\n      ) => {\n        return await this._interface.sendClientRequest(path, requestOptions, await this._getSession(), requestType);\n      },\n    };\n  };\n}\n"], "mappings": ";AAIA,SAAS,eAAe,qBAAqB,yBAAyB;AACtE,SAAS,aAAa,4BAA4B;AAYlD,SAAS,uBAAuB;AAEhC,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB,cAAc,gBAAgB;AAC5D,SAAS,uBAAuB;AAEhC,SAAS,iBAAiB,YAAY;AACtC,SAAS,cAAc,mBAAmB,YAAY;AACtD,SAAS,SAAS,oBAAoB;AACtC,SAAS,cAAc;AACvB,SAAS,OAAO,iBAAiB;AACjC,SAAS,UAAU,yBAAyB;AAC5C,SAAS,iBAAiB,kBAAkB;AAC5C,SAAS,oBAAoB;AAC7B,YAAY,YAAY;AAExB,SAAS,4BAA4B;AACrC,SAAS,4BAA4B,mBAAmB,uBAAuB;AAC/E,SAAuB,2BAA2B,oBAAoB,+BAA+B,oBAAoB,iBAAiB,mBAAmB,+BAA+B;AAC5L,SAA6D,mCAAmC;AAChG,SAA2H,+BAA+B;AAE1J,SAAmF,mCAAmC,yCAAyC;AAE/J,SAAgE,uCAAuC;AACvG,SAA0G,yBAAyB,+BAA+B;AAClK,SAA0H,+BAA+B;AAGzJ,SAAsB,eAAe,aAAa,sBAAsB,uBAAuB,YAAY,+BAA+B,qBAAqB,gCAAgC,eAAgB;AAE/M,OAAO,SAAS,aAAa,eAAe;AAC5C,SAAS,qBAAqB;AAE9B,IAAI,gBAAgB;AAGpB,IAAM,UAAW,WAAmB,WAAW,EAAE,KAAK,CAAC,EAAE;AAGzD,IAAM,gBAAgB,oBAAI,IAA8E;AAEjG,IAAM,iCAAN,MAAM,+BAAoJ;AAAA,EAqM/J,YAA+B,UAW7B;AAX6B;AA3L/B,SAAU,oBAAwC;AAOlD,SAAQ,mCAAmC;AAC3C,SAAiB,kBAAkB,IAAI,gBAAwF;AAE/H,SAAiB,oBAAoB,qBAAqB,OAAO,YAAY;AAC3E,UAAI,KAAK,kCAAkC;AACzC,cAAM,KAAK,GAAI;AAAA,MACjB;AACA,UAAI,QAAQ,mBAAmB,GAAG;AAiBhC,eAAO;AAAA,MACT;AACA,aAAO,MAAM,KAAK,WAAW,qBAAqB,OAAO;AAAA,IAC3D,CAAC;AACD,SAAiB,uBAAuB,YAAY,YAAY;AAC9D,aAAO,OAAO,QAAQ,MAAM,KAAK,WAAW,iBAAiB,CAAC;AAAA,IAChE,CAAC;AACD,SAAiB,sBAAsB,qBAAqB,OAAO,YAAY;AAC7E,aAAO,MAAM,KAAK,WAAW,aAAa,OAAO;AAAA,IACnD,CAAC;AACD,SAAiB,+BAA+B,qBAG9C,OAAO,SAAS,CAAC,QAAQ,SAAS,MAAM;AACxC,aAAO,MAAM,KAAK,WAAW,+BAA+B,EAAE,QAAQ,UAAU,GAAG,OAAO;AAAA,IAC5F,CAAC;AACD,SAAiB,sCAAsC,qBAGrD,OAAO,SAAS,CAAC,SAAS,MAAM;AAChC,aAAO,MAAM,KAAK,WAAW,kCAAkC,EAAE,UAAU,GAAG,OAAO;AAAA,IACvF,CAAC;AACD,SAAiB,yBAAyB,qBAAqB,OAAO,YAAY;AAChF,aAAO,MAAM,KAAK,WAAW,qBAAqB,OAAO;AAAA,IAC3D,CAAC;AACD,SAAiB,+CAA+C;AAAA,MAC9D,OAAO,SAAS,CAAC,YAAY,KAAK,MAAM;AACtC,YAAI;AACF,gBAAM,SAAS,MAAM,KAAK,WAAW,0BAA0B,YAAY,SAAS,IAAI,OAAO;AAC/F,iBAAO,EAAE,aAAa,OAAO,aAAa;AAAA,QAC5C,SAAS,KAAK;AACZ,cAAI,EAAE,YAAY,wCAAwC,WAAW,GAAG,KAAK,YAAY,kCAAkC,WAAW,GAAG,IAAI;AAC3I,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAiB,mCAAmC;AAAA,MAClD,OAAO,SAAS,CAAC,YAAY,OAAO,QAAQ,MAAM;AAChD,eAAO,MAAM,KAAK,+BAA+B;AAAA,UAC/C,SAAS,YAAY,OAAO,QAAQ,MAAM,KAAK,kBAAkB,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AAAA,UACnG,qBAAqB,YAAY,OAAO,QAAQ,MAAM,KAAK,6CAA6C,UAAU,CAAC,SAAS,YAAY,SAAS,EAAE,GAAY,YAAY,CAAC;AAAA,UAC5K,eAAe,MAAM,cAAc,KAAK,8CAA8C,CAAC,SAAS,YAAY,SAAS,EAAE,GAAY,eAAe;AAAA,UAClJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAiB,2BAA2B;AAAA,MAC1C,OAAO,SAAS,CAAC,MAAM,MAAM;AAC3B,eAAO,MAAM,KAAK,WAAW,uBAAuB,EAAE,OAAO,GAAG,OAAO;AAAA,MACzE;AAAA,IACF;AACA,SAAiB,wBAAwB;AAAA,MACvC,OAAO,SAAS,CAAC,MAAM,MAAM;AAC3B,eAAO,MAAM,KAAK,WAAW,oBAAoB,EAAE,OAAO,GAAG,OAAO;AAAA,MACtE;AAAA,IACF;AACA,SAAiB,+BAA+B;AAAA,MAC9C,OAAO,SAAS,CAAC,MAAM,MAAM;AAC3B,eAAO,MAAM,KAAK,WAAW,qBAAqB,EAAE,QAAQ,QAAQ,KAAK,GAAG,OAAO;AAAA,MACrF;AAAA,IACF;AACA,SAAiB,8BAA8B;AAAA,MAC7C,OAAO,YAAY;AACjB,eAAO,MAAM,KAAK,WAAW,0BAA0B,OAAO;AAAA,MAChE;AAAA,IACF;AAEA,SAAiB,oBAAoB;AAAA,MACnC,OAAO,YAAY;AACjB,cAAM,UAAU,MAAM,KAAK,WAAW,mBAAmB,EAAE,SAAS,KAAK,GAAG,SAAS,QAAQ;AAC7F,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAiB,oBAAoB;AAAA,MACnC,OAAO,SAAS,CAAC,MAAM,MAAM;AAC3B,cAAM,UAAU,MAAM,KAAK,WAAW,mBAAmB,EAAE,SAAS,OAAO,GAAG,SAAS,QAAQ;AAC/F,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAQ,6BAA4F;AA+IpG,SAAU,oBAAoB,sBAAsB;AACpD,SAAU,gCAAgC,oBAAI,QAAoC;AAClF,SAAU,sBAAsB,oBAAI,QAAyC;AAC7E,SAAU,iCAA4D;AAuKtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAQ,qCAAqC,oBAAI,QAA0D;AAvOzG,QAAI,CAAC,+BAA8B,sBAAsB,OAAO;AAC9D,YAAM,IAAI,oBAAoB,2SAA2S;AAAA,IAC3U;AAEA,QAAI,eAAe,UAAU;AAC3B,WAAK,aAAa,SAAS;AAAA,IAC7B,OAAO;AACL,WAAK,aAAa,IAAI,qBAAqB;AAAA,QACzC,YAAY,MAAM,WAAW,SAAS,OAAO;AAAA,QAC7C,qBAAqB,SAAS,uBAAuB,8BAA8B;AAAA,QACnF,WAAW,SAAS,aAAa,oBAAoB;AAAA,QACrD;AAAA,QACA,sBAAsB,SAAS,wBAAwB,+BAA+B;AAAA,QACtF,gBAAgB,YAAY;AAAA,QAC5B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,kBAAkB,SAAS;AAChC,SAAK,kBAAkB,SAAS,kBAAkB;AAClD,SAAK,cAAc,SAAS,QAAQ,CAAC;AACrC,SAAK,uBAAuB,SAAS,uBAAuB,CAAC;AAE7D,QAAI,SAAS,kBAAkB;AAC7B,WAAK,oBAAoB,SAAS;AAClC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EA3GA,MAAgB,sBAA6C;AAC3D,QAAI,KAAK,oBAAoB,mBAAmB,KAAK,oBAAoB,UAAU;AACjF,aAAO,MAAM,mBAAmB;AAAA,IAClC,OAAO;AACL,aAAO,MAAM,8BAA8B;AAAA,IAC7C;AAAA,EACF;AAAA,EAEA,MAAgB,+BAA+B,SAMmC;AAChF,UAAM,OAAO,MAAM,QAAQ,QAAQ;AACnC,QAAI,gBAAgB;AACpB,QAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ,UAAU,GAAG;AAC3E,sBAAgB;AAAA,IAClB;AAEA,UAAM,QAAQ,MAAM,QAAQ,oBAAoB;AAChD,QAAI,CAAC,OAAO;AACV,sBAAgB;AAAA,IAClB;AAEA,QAAI,CAAC,iBAAiB,QAAQ,UAAU;AACtC,UAAI,CAAC,QAAQ,SAAS;AACpB,cAAM,IAAI,MAAM;AAAA;AAAA;AAAA;AAAA,SAIf;AAAA,MACH;AACA,YAAM;AAAA,QACF,KAAK;AAAA,QACL;AAAA,UACE,UAAU,QAAQ;AAAA,UAClB,aAAa,KAAK,KAAK;AAAA,UACvB,kBAAkB,KAAK,KAAK;AAAA,UAC5B,eAAe,kBAAkB,QAAQ,SAAS,KAAK,KAAK,qBAAqB,QAAQ,UAAU,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC;AAAA,QACvH;AAAA,QACA,QAAQ;AAAA,MACV;AACF,aAAO,MAAM,aAAa;AAAA,IAC5B,WAAW,CAAC,eAAe;AACzB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,IAAI,QAAQ;AAAA,MACZ,MAAM,iBAAiB;AACrB,cAAM,SAAS,MAAM,QAAQ,oBAAoB;AACjD,YAAI,CAAC,QAAQ;AACX,gBAAM,IAAI,oBAAoB,2BAA2B;AAAA,QAC3D;AACA,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AACf,cAAM,SAAS,QAAQ,cAAc;AACrC,YAAI,CAAC,QAAQ;AACX,gBAAM,IAAI,oBAAoB,2BAA2B;AAAA,QAC3D;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EA2CU,wBAAwB;AAChC,QAAI,CAAC,KAAK,mBAAmB;AAC3B,YAAM,IAAI,oBAAoB,mCAAmC;AAAA,IACnE;AACA,QAAI,cAAc,IAAI,KAAK,iBAAiB,GAAG;AAC7C,YAAM,IAAI,oBAAoB,mEAAmE;AAAA,IACnG;AACA,kBAAc,IAAI,KAAK,mBAAmB,CAAC,KAAK,SAAS,eAAe,QAAW,IAAI,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,uBAAuB;AAC/B,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB,aAAa;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAgB,qBAAqB,MAAc,SAAc;AAC/D,WAAO,MAAM,KAAK,WAAW,oBAAoB,EAAE,GAAG,SAAS,KAAK,CAAC;AAAA,EACvE;AAAA,EAEU,wBAAwB,MAAc,SAAqB;AACnE,sBAAkB,KAAK,qBAAqB,MAAM,OAAO,CAAC;AAC1D,UAAM,IAAI,oBAAoB,GAAG,IAAI,sFAAsF;AAAA,EAC7H;AAAA,EAMA,IAAc,0BAA0B;AACtC,WAAO,iBAAiB,KAAK,SAAS;AAAA,EACxC;AAAA,EACU,sBAAsB,SAA+F;AAC7H,UAAM,eAAe,QAAQ;AAC7B,UAAM,oBAAoB,QAAQ,mBAAmB,WAAW,IAAK,IAAI,KAAK,MAAM,QAAQ,iBAAiB,IAAI;AACjH,UAAM,cAAc,qBAAqB,iBAAiB,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,IAAI;AACxG,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAc,yBAAyB;AAKrC,WAAO;AAAA,EACT;AAAA,EACU,8BAAkD;AAC1D,QAAI,CAAC,cAAc,GAAG;AACpB,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAChE;AAEA,QAAI,KAAK,mCAAmC,MAAM;AAChD,YAAM,kBAAkB,CAAC,QAA4B;AACnD,cAAM,SAAS,KAAK,sBAAsB;AAAA,UACxC,oBAAoB,gBAAgB,KAAK,uBAAuB,KAAK,gBAAgB,eAAe;AAAA;AAAA,UACpG,mBAAmB,gBAAgB,KAAK,sBAAsB;AAAA,QAChE,CAAC;AACD,eAAO;AAAA,UACL,cAAc,OAAO;AAAA,UACrB,aAAa,OAAO,gBAAgB,KAAK,iBAAiB,OAAO,eAAe,IAAI,cAAc;AAAA,QACpG;AAAA,MACF;AACA,WAAK,iCAAiC,IAAI,MAAmB,gBAAgB,IAAI,CAAC;AAClF,UAAI,wBAAwB;AAE5B,kBAAY,MAAM;AAChB,YAAI,uBAAuB;AACzB,gBAAM,WAAW,KAAK,+BAAgC,IAAI;AAC1D,gBAAM,eAAe,gBAAgB,QAAQ;AAC7C,cAAI,CAAC,gBAAgB,cAAc,QAAQ,GAAG;AAC5C,iBAAK,+BAAgC,IAAI,YAAY;AAAA,UACvD;AAAA,QACF;AAAA,MACF,GAAG,GAAG;AACN,WAAK,+BAA+B,SAAS,CAAC,UAAU;AACtD,YAAI;AACF,kCAAwB,KAAK,yBAAyB,MAAM,cAAc,EAAE,QAAQ,KAAK,KAAK,KAAK,IAAI,CAAC;AACxG,kCAAwB,KAAK,wBAAwB,MAAM,cAAc,KAAK,UAAU,CAAC,MAAM,cAAc,MAAM,WAAW,CAAC,IAAI,MAAM,EAAE,QAAQ,KAAK,KAAK,GAAG,CAAC;AACjK,6BAAmB,eAAe;AAClC,kCAAwB;AAAA,QAC1B,SAAS,GAAG;AACV,cAAI,CAAC,cAAc,GAAG;AAEpB,oCAAwB;AAAA,UAC1B,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,KAAK;AAAA,EACd;AAAA,EACU,uBAAuB,cAA4B,wBAA6D;AACxH,UAAM,iBAAiB,2BAA2B,SAAY,KAAK,kBAAkB;AAErF,YAAQ,gBAAgB;AAAA,MACtB,KAAK,UAAU;AACb,eAAO,KAAK,4BAA4B;AAAA,MAC1C;AAAA,MACA,KAAK,iBAAiB;AACpB,YAAI,cAAc,GAAG;AACnB,iBAAO,KAAK,4BAA4B;AAAA,QAC1C,OAAO;AACL,gBAAM,SAAS,KAAK,sBAAsB;AAAA,YACxC,oBAAoB,aAAa,IAAI,KAAK,uBAAuB,KAAK,aAAa,IAAI,eAAe;AAAA;AAAA,YACtG,mBAAmB,aAAa,IAAI,KAAK,sBAAsB;AAAA,UACjE,CAAC;AACD,gBAAM,QAAQ,IAAI,MAAmB,MAAM;AAC3C,gBAAM,SAAS,CAAC,UAAU;AACxB,8BAAkB,YAAY;AAY5B,oBAAM,QAAQ,IAAI;AAAA,gBAChB,kBAAkB,KAAK,yBAAyB,MAAM,cAAc,EAAE,QAAQ,KAAK,KAAK,KAAK,KAAK,uBAAuB,KAAK,CAAC;AAAA,gBAC/H,kBAAkB,KAAK,wBAAwB,MAAM,cAAc,KAAK,UAAU,CAAC,MAAM,cAAc,MAAM,WAAW,CAAC,IAAI,MAAM,EAAE,QAAQ,KAAK,KAAK,IAAI,uBAAuB,KAAK,CAAC;AAAA,cAC1L,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAK,UAAU;AACb,eAAO,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AACP,YAAI,mBAAmB,MAAM;AAC3B,iBAAO,sBAAsB;AAAA,QAC/B,WAAW,OAAO,mBAAmB,YAAY,aAAa,gBAAgB;AAC5E,cAAI,KAAK,oBAAoB,IAAI,cAAc,EAAG,QAAO,KAAK,oBAAoB,IAAI,cAAc;AAGpG,gBAAM,kBAAkB,eAAe,QAAQ,IAAI,cAAc;AACjE,cAAI,iBAAiB;AACnB,gBAAIA;AACJ,gBAAI;AACF,cAAAA,UAAS,KAAK,MAAM,eAAe;AACnC,kBAAI,OAAOA,YAAW,SAAU,OAAM,IAAI,MAAM,2CAA2C;AAC3F,kBAAIA,YAAW,KAAM,OAAM,IAAI,MAAM,sCAAsC;AAAA,YAC7E,SAAS,GAAG;AACV,oBAAM,IAAI,MAAM,gCAAgC,eAAe,IAAI,EAAE,OAAO,EAAE,CAAC;AAAA,YACjF;AACA,mBAAO,KAAK,uBAAuB,cAAc;AAAA,cAC/C,aAAaA,QAAO,eAAe;AAAA,cACnC,cAAcA,QAAO,gBAAgB;AAAA,YACvC,CAAC;AAAA,UACH;AAGA,gBAAM,eAAe,eAAe,QAAQ,IAAI,QAAQ;AACxD,gBAAM,SAAgB,aAAM,gBAAgB,EAAE;AAC9C,gBAAM,MAAM,IAAI,MAAmB;AAAA,YACjC,cAAc,OAAO,KAAK,uBAAuB,KAAK,OAAO,eAAe,KAAK;AAAA;AAAA,YACjF,aAAa,OAAO,KAAK,sBAAsB,KAAK;AAAA,UACtD,CAAC;AACD,eAAK,oBAAoB,IAAI,gBAAgB,GAAG;AAChD,iBAAO;AAAA,QACT,WAAW,iBAAiB,kBAAkB,kBAAkB,gBAAgB;AAC9E,iBAAO,IAAI,MAAmB;AAAA,YAC5B,cAAc,eAAe;AAAA,YAC7B,aAAa,eAAe;AAAA,UAC9B,CAAC;AAAA,QACH;AAEA,cAAM,IAAI,MAAM,uBAAuB,cAAc,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEU,eAAe,wBAA6D;AACpF,iBAAa;AACb,UAAM,eAAe,0BAA0B;AAC/C,UAAM,aAAa,KAAK,uBAAuB,cAAc,sBAAsB;AACnF,WAAO;AAAA,EACT;AAAA,EAWU,0BAA0B,YAAiD;AACnF,UAAM,WAAW,WAAW,IAAI;AAChC,UAAM,aAAa,gBAAgB,oBAAoB,QAAQ;AAC/D,UAAM,WAAW,aAAa,KAAK,mCAAmC,IAAI,UAAU,GAAG,IAAI,UAAU,IAAI;AACzG,QAAI,SAAU,QAAO;AAErB,UAAM,UAAU,KAAK,WAAW,cAAc;AAAA,MAC5C,cAAc,SAAS;AAAA,MACvB,aAAa,SAAS;AAAA,IACxB,CAAC;AACD,YAAQ,oBAAoB,CAAC,mBAAmB;AAC9C,iBAAW,OAAO,CAAC,SAAS;AAAA,QAC1B,GAAG;AAAA,QACH,aAAa,gBAAgB,SAAS;AAAA,MACxC,EAAE;AAAA,IACJ,CAAC;AACD,YAAQ,aAAa,MAAM;AACzB,iBAAW,OAAO,CAAC,SAAS;AAAA,QAC1B,GAAG;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ,CAAC;AAED,QAAI,uBAAuB,KAAK,mCAAmC,IAAI,UAAU,KAAK,oBAAI,IAAI;AAC9F,SAAK,mCAAmC,IAAI,YAAY,oBAAoB;AAC5E,yBAAqB,IAAI,YAAY,OAAO;AAC5C,WAAO;AAAA,EACT;AAAA,EAEA,MAAgB,YAAY,wBAAmE;AAC7F,UAAM,aAAa,KAAK,uBAAuB,MAAM,KAAK,oBAAoB,GAAG,sBAAsB;AACvG,WAAO,KAAK,0BAA0B,UAAU;AAAA,EAClD;AAAA,EAEU,YAAY,wBAA0D;AAC9E,UAAM,aAAa,KAAK,eAAe,sBAAsB;AAC7D,UAAM,YAAY,YAAY,CAAC,OAAmB;AAChD,YAAM,EAAE,YAAY,IAAI,WAAW,SAAS,MAAM;AAChD,WAAG;AAAA,MACL,CAAC;AACD,aAAO;AAAA,IACT,GAAG,CAAC,UAAU,CAAC;AACf,UAAM,cAAc,YAAY,MAAM,KAAK,0BAA0B,UAAU,GAAG,CAAC,UAAU,CAAC;AAC9F,WAAO,MAAM,qBAAqB,WAAW,aAAa,WAAW;AAAA,EACvE;AAAA,EAEA,MAAgB,2BAA2B,QAA8D;AACvG,QAAI,EAAE,iBAAiB,WAAW,EAAE,kBAAkB,SAAS;AAC7D,YAAM,IAAI,oBAAoB,kDAAkD,EAAE,OAAO,CAAC;AAAA,IAC5F;AACA,UAAM,aAAa,KAAK,uBAAuB,MAAM,KAAK,oBAAoB,CAAC;AAC/E,eAAW,IAAI,MAAM;AAAA,EACvB;AAAA,EAEU,yBAAyB,wBAAkF;AACnH,YAAQ,2BAA2B,SAAY,yBAAyB,KAAK,qBAAqB;AAAA,EACpG;AAAA,EAEU,4BAA4B,wBAA2F;AAC/H,QAAI,CAAC,KAAK,yBAAyB,sBAAsB,GAAG;AAC1D,YAAM,IAAI,MAAM,uVAAuV;AAAA,IACzW;AAAA,EACF;AAAA,EAEU,qBAAwD;AAChE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EAEU,yBAAoE;AAC5E,QAAI,CAAC,KAAK,mBAAmB,GAAG;AAC9B,YAAM,IAAI,MAAM,mFAAmF;AAAA,IACrG;AAAA,EACF;AAAA,EAEU,uBAAuB,MAAqD;AACpF,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,QAAQ;AAAA,QACN,eAAe,KAAK,OAAO;AAAA,QAC3B,mBAAmB,KAAK,OAAO;AAAA,QAC/B,kBAAkB,KAAK,OAAO;AAAA,QAC9B,gBAAgB,KAAK,OAAO;AAAA,QAC5B,2BAA2B,KAAK,OAAO;AAAA,QACvC,2BAA2B,KAAK,OAAO;AAAA,QACvC,kBAAkB,KAAK,OAAO;AAAA,QAC9B,kBAAkB,KAAK,OAAO;AAAA,QAC9B,gBAAgB,KAAK,OAAO,wBAAwB,IAAI,CAAC,OAAO;AAAA,UAC9D,IAAI,EAAE;AAAA,QACR,EAAE;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EAEU,0BAA0B,MAAwG;AAC1I,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,IACX;AAAA,EACF;AAAA,EAEU,wBAAwB,MAA0D;AAC1F,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa;AAAA,QACX,aAAa,KAAK;AAAA,QAClB,iBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EAEU,8BAA8B,SAA0B,MAA4D;AAC5H,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,gBAAgB,KAAK;AAAA,MACrB,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,QAAQ,YAAY;AAClB,cAAM,KAAK,WAAW,qBAAqB,KAAK,IAAI,KAAK,SAAS,OAAO;AACzE,cAAM,KAAK,sBAAsB,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AAAA,EAEU,oBACR,MACyG;AACzG,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,WAAW,KAAK,oBAAoB,IAAI,KAAK,KAAK,iBAAiB,IAAI;AAAA,MACvE,mBAAmB,KAAK,6BAA6B,IAAI,KAAK,KAAK,0BAA0B,IAAI;AAAA,MACjG,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,GAAI,KAAK,SAAS,SAAS,EAAE,MAAM,QAAQ,QAAQ,KAAK,QAAQ,IAAI,EAAE,MAAM,QAAQ,QAAQ,KAAK,QAAQ;AAAA,MACzG,OAAO,OAAO,KAAK,UAAU,WAAW,KAAK,QAAQ;AAAA,QACnD,UAAU,KAAK,MAAM;AAAA,MACvB;AAAA,MACA,SAAS,WAAW;AAClB,eAAO,KAAK,WAAW,MAAM;AAAA,MAC/B;AAAA,MACA,YAAY,WAAW;AACrB,YAAI,KAAK,mBAAmB;AAC1B,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,aAAa,KAAK,YAAY,oBAAI,KAAK,GAAG;AACjD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EAOU,sBAAsB,SAA0B,MAA2N;AACnR,WAAO;AAAA,MACL,GAAG,KAAK,oBAAoB,IAAI;AAAA,MAChC,MAAM,SAAS;AACb,cAAM,KAAK,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,MACrC;AAAA,MACA,QAAQ,OAAO,YAAiC;AAC9C,cAAM,KAAK,WAAW,oBAAoB,KAAK,SAAS,SAAS,EAAE,SAAS,KAAK,QAAQ,IAAI,EAAE,SAAS,KAAK,QAAQ,GAAG,KAAK,IAAI,SAAS,SAAS,QAAQ;AAC3J,YAAI,KAAK,SAAS,QAAQ;AACxB,gBAAM,KAAK,kBAAkB,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,QAC9D,OAAO;AACL,gBAAM,KAAK,kBAAkB,QAAQ,CAAC,OAAO,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEU,oBAAoB,MAAmC,SAAgC;AAC/F,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,iBAAiB,KAAK;AAAA,MACtB,gBAAgB,KAAK;AAAA,MACrB,wBAAwB,KAAK;AAAA,MAC7B,MAAM,WAAW,SAAkD;AACjE,cAAM,IAAI,WAAW,mBAAmB;AAAA,UACtC,QAAQ,KAAK;AAAA,UACb,OAAO,QAAQ;AAAA,UACf;AAAA,UACA,aAAa,QAAQ,eAAe,qBAAqB,IAAI,KAAK,gBAAgB,aAAa;AAAA,QACjG,CAAC;AACD,cAAM,IAAI,sBAAsB,QAAQ,CAAC,SAAS,KAAK,EAAE,CAAC;AAAA,MAC5D;AAAA,MACA,MAAM,YAAY;AAChB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,yBAAyB,UAAU,CAAC,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC;AAC5G,eAAO,OAAO,IAAI,CAACC,UAAS,IAAI,wBAAwBA,KAAI,CAAC;AAAA,MAC/D;AAAA,MACA,WAAW;AACT,cAAM,SAAS,cAAc,IAAI,0BAA0B,CAAC,SAAS,KAAK,EAAE,GAAY,iBAAiB;AACzG,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,wBAAwBA,KAAI,CAAC;AAAA,MAC/D;AAAA,MACA,MAAM,kBAAkB;AACtB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,sBAAsB,UAAU,CAAC,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC;AACzG,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8B,SAASA,KAAI,CAAC;AAAA,MAC9E;AAAA,MACA,iBAAiB;AACf,cAAM,SAAS,cAAc,IAAI,uBAAuB,CAAC,SAAS,KAAK,EAAE,GAAY,uBAAuB;AAC5G,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8B,SAASA,KAAI,CAAC;AAAA,MAC9E;AAAA,MACA,MAAM,OAAO,MAAwB;AACnC,cAAM,IAAI,WAAW,WAAW,EAAE,MAAM,wBAAwB,IAAI,GAAG,QAAQ,KAAK,GAAG,GAAG,OAAO;AACjG,cAAM,IAAI,uBAAuB,QAAQ,CAAC,OAAO,CAAC;AAAA,MACpD;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,WAAW,KAAK,IAAI,OAAO;AAChD,cAAM,IAAI,uBAAuB,QAAQ,CAAC,OAAO,CAAC;AAAA,MACpD;AAAA,MAEA,aAAa;AACX,cAAM,SAAS,cAAc,IAAI,mBAAmB,CAAC,SAAS,KAAK,EAAE,GAAY,mBAAmB;AACpG,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,sBAAsB,SAASA,KAAI,CAAC;AAAA,MACtE;AAAA,MAEA,MAAM,cAAc;AAClB,cAAM,UAAU,OAAO,QAAQ,MAAM,IAAI,kBAAkB,UAAU,CAAC,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC;AACtG,eAAO,QAAQ,IAAI,CAACA,UAAS,IAAI,sBAAsB,SAASA,KAAI,CAAC;AAAA,MACvE;AAAA,MAEA,MAAM,aAAa,SAAwC;AACzD,cAAM,SAAS,MAAM,IAAI,WAAW;AAAA,UAClC,MAAM,4BAA4B,QAAQ,KAAK,IAAI,OAAO;AAAA,UAC1D;AAAA,UACA;AAAA,QACF;AACA,cAAM,IAAI,kBAAkB,QAAQ,CAAC,SAAS,KAAK,EAAE,CAAC;AACtD,eAAO,IAAI,sBAAsB,SAAS,MAAM;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EAEU,8BAA8B,MAA6C,SAA0C;AAC7H,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,aAAa,KAAK;AAAA,MAElB,MAAM,sBAAsB,SAAoC;AAC9D,cAAM,IAAI,WAAW;AAAA,UACnB,KAAK;AAAA,UACL,SAAS,eAAe,qBAAqB,IAAI,KAAK,mBAAmB,aAAa;AAAA,UACtF;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,OAAO,MAAmC;AAC9C,cAAM,IAAI,WAAW,2BAA2B,KAAK,IAAI,kCAAkC,IAAI,GAAG,OAAO;AACzG,cAAM,IAAI,4BAA4B,QAAQ,CAAC,OAAO,CAAC;AAAA,MACzD;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,2BAA2B,KAAK,IAAI,OAAO;AAChE,cAAM,IAAI,4BAA4B,QAAQ,CAAC,OAAO,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EACU,YAAY,SAAgC;AACpD,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,QACd,MAAM,YAAY;AAChB,gBAAM,SAAS,MAAM,QAAQ,4BAA4B,GAAM;AAC/D,iBAAO;AAAA,YACL,aAAa,QAAQ,YAAY,SAAS;AAAA,YAC1C,cAAc,QAAQ,cAAc,SAAS;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,iBAAsD;AAC1D,eAAO;AAAA,UACL,gBAAgB,KAAK,UAAU,MAAM,KAAK,YAAY,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,MACA,MAAM,cAAoF;AACxF,cAAM,SAAS,MAAM,KAAK,eAAe,UAAU;AACnD,eAAO;AAAA,MACT;AAAA,MACA,MAAM,gBAAgB,SAA6I;AACjK,cAAM,YAAY,MAAM,IAAI,eAAe,IAAI;AAC/C,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,oBAAoB,4EAA4E;AAAA,QAC5G;AAEA,cAAM,mBAAmB,MAAM,IAAI,WAAW,4BAA4B,CAAC,GAAG,OAAO;AAErF,YAAI,iBAAiB,WAAW,MAAM;AACpC,iBAAO,OAAO,MAAM,IAAI,YAAY,0BAA0B,2DAA2D,CAAC;AAAA,QAC5H;AAEA,cAAM,EAAE,cAAc,KAAK,IAAI,iBAAiB;AAGhD,YAAI,aAAa,GAAG,OAAO,2CAA2C;AACpE,gBAAM,IAAI,oBAAoB,oEAAoE,aAAa,GAAG,EAAE,EAAE;AAAA,QACxH;AAEA,qBAAa,GAAG,KAAK;AAErB,YAAI;AACJ,YAAI;AACF,oBAAU,MAAM,kBAAkB,EAAE,aAAa,aAAa,CAAC;AAAA,QACjE,SAAS,OAAY;AACnB,cAAI,iBAAiB,eAAe;AAClC,mBAAO,OAAO,MAAM,IAAI,YAAY,qBAAqB,MAAM,SAAS,MAAM,IAAI,CAAC;AAAA,UACrF,OAAO;AAEL,yBAAa,+BAA+B,KAAK;AACjD,mBAAO,OAAO,MAAM,IAAI,YAAY,0BAA0B,2DAA2D,CAAC;AAAA,UAC5H;AAAA,QACF;AAGA,cAAM,qBAAqB,MAAM,IAAI,WAAW,gBAAgB,EAAE,YAAY,SAAS,KAAK,GAAG,OAAO;AAEtG,cAAM,IAAI,aAAa,OAAO;AAC9B,eAAO;AAAA,MACT;AAAA,MACA,QAAQ,SAA0C;AAChD,eAAO,IAAI,SAAS,SAAS,OAAO;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EAEU,6BAA6B,MAAgD,SAAqD;AAC1I,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,iBAAiB,KAAK;AAAA,MACtB,MAAM,OAAO,QAA4D;AACvE,cAAM,IAAI,WAAW,wBAAwB;AAAA,UAC3C,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,SAAS;AAAA,YACP,cAAc,OAAO;AAAA,YACrB,mBAAmB,OAAO;AAAA,UAC5B;AAAA,QACF,GAAG,OAAO;AACV,cAAM,IAAI,6BAA6B,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,MACxE;AAAA,IACF;AAAA,EACF;AAAA,EAEU,gBAAgB,MAA8F;AACtH,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,sBAAsB,KAAK;AAAA,MAC3B,iBAAiB,KAAK;AAAA,MACtB,YAAY,IAAI,KAAK,KAAK,mBAAmB;AAAA,MAC7C,gBAAgB,KAAK;AAAA,MACrB,wBAAwB,KAAK;AAAA,MAC7B,aAAa,KAAK;AAAA,MAClB,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK;AAAA,MACrB,gBAAgB,KAAK;AAAA,MACrB,oBAAoB,KAAK;AAAA,MACzB,uBAAuB,KAAK;AAAA,MAC5B,aAAa,KAAK;AAAA,MAClB,eAAkD;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA,EAEU,4BAA4B,MAAsD,SAAqC;AAC/H,UAAM,MAAM;AAGZ,mBAAe,oBAAoB,IAAkB,SAAmF;AACtI,YAAM,cAAc,SAAS,QAAQ,KAAK,GAAG;AAC7C,aAAO,OAAO,QAAQ,MAAM,IAAI,iCAAiC,UAAU,CAAC,SAAS,IAAI,eAAe,IAAI,SAAS,OAAO,UAAU,GAAG,YAAY,CAAC;AAAA,IACxJ;AAIA,aAAS,oBAAoB,IAAkB,SAA0E;AACvH,YAAM,cAAc,SAAS,QAAQ,KAAK,GAAG;AAC7C,aAAO,cAAc,IAAI,kCAAkC,CAAC,SAAS,IAAI,eAAe,IAAI,SAAS,OAAO,UAAU,GAAY,4BAA4B;AAAA,IAChK;AACA,WAAO;AAAA,MACL,MAAM,oBAAoB;AACxB,cAAM,WAAW,MAAM,IAAI,WAAW,aAAa,OAAO;AAC1D,eAAO,SAAS,MAAM,IAAI,CAACA,UAAS,IAAI,uBAAuBA,KAAI,CAAC;AAAA,MACtE;AAAA,MACA,MAAM,cAAc,WAAmB;AACrC,cAAM,IAAI,WAAW,cAAc,WAAW,OAAO;AAAA,MACvD;AAAA,MACA,eAAe,aAAqB;AAClC,eAAO,KAAK,OAAO,EAAE,YAAY,CAAC;AAAA,MACpC;AAAA,MACA,kBAAkB,UAA+B;AAC/C,eAAO,KAAK,OAAO,EAAE,gBAAgB,SAAS,CAAC;AAAA,MACjD;AAAA,MACA,MAAM,gBAAgB,MAAmB;AACvC,cAAM,KAAK,OAAO,EAAE,gBAAgB,MAAM,MAAM,KAAK,CAAC;AAAA,MACxD;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA,MAAM,QAAQ,QAAgB;AAC5B,cAAM,QAAQ,MAAM,KAAK,UAAU;AACnC,eAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,MAC/C;AAAA,MACA,QAAQ,QAAgB;AACtB,cAAM,QAAQ,KAAK,SAAS;AAC5B,eAAO,QAAQ,MAAM;AACnB,iBAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,QAC/C,GAAG,CAAC,OAAO,MAAM,CAAC;AAAA,MACpB;AAAA,MACA,MAAM,YAAY;AAChB,cAAM,QAAQ,OAAO,QAAQ,MAAM,IAAI,uBAAuB,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AAChG,eAAO,MAAM,IAAI,CAACA,UAAS,IAAI,oBAAoBA,OAAM,OAAO,CAAC;AAAA,MACnE;AAAA,MACA,WAAW;AACT,cAAM,QAAQ,cAAc,IAAI,wBAAwB,CAAC,OAAO,GAAG,iBAAiB;AACpF,eAAO,QAAQ,MAAM,MAAM,IAAI,CAACA,UAAS,IAAI,oBAAoBA,OAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAAA,MAC3F;AAAA,MACA,MAAM,WAAW,MAAyB;AACxC,cAAMA,QAAO,MAAM,IAAI,WAAW,iBAAiB,wBAAwB,MAAM,IAAI,GAAG,OAAO;AAC/F,cAAM,IAAI,uBAAuB,QAAQ,CAAC,OAAO,CAAC;AAClD,cAAM,KAAK,OAAO,EAAE,gBAAgBA,MAAK,GAAG,CAAC;AAC7C,eAAO,IAAI,oBAAoBA,OAAM,OAAO;AAAA,MAC9C;AAAA,MACA,MAAM,UAAU,MAAY;AAC1B,cAAM,IAAI,WAAW,UAAU,KAAK,IAAI,OAAO;AAAA,MAEjD;AAAA,MACA,MAAM,gBAAgB,gBAAiD,SAA8D;AACnI,YAAI,kBAAkB,QAAQ,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,YAAY,SAAS,aAAa;AACxC,gBAAM,cAAc,OAAO,QAAQ,MAAM,IAAI,6BAA6B,UAAU,CAAC,SAAS,MAAM,IAAI,SAAS,GAAG,YAAY,CAAC;AACjI,iBAAO,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC;AAAA,QACtE,OAAO;AACL,gBAAM,OAAO;AACb,gBAAM,YAAY,MAAM,aAAa;AACrC,gBAAM,cAAc,OAAO,QAAQ,MAAM,IAAI,oCAAoC,UAAU,CAAC,SAAS,SAAS,GAAG,YAAY,CAAC;AAC9H,iBAAO,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC;AAAA,QACtE;AAAA,MACF;AAAA,MACA,eAAe,gBAAiD,SAAqD;AACnH,YAAI,kBAAkB,QAAQ,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,YAAY,SAAS,aAAa;AACxC,gBAAM,cAAc,cAAc,IAAI,8BAA8B,CAAC,SAAS,MAAM,IAAI,SAAS,GAAY,uBAAuB;AACpI,iBAAO,QAAQ,MAAM,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC,GAAG,CAAC,WAAW,CAAC;AAAA,QACpG,OAAO;AACL,gBAAM,OAAO;AACb,gBAAM,YAAY,MAAM,aAAa;AACrC,gBAAM,cAAc,cAAc,IAAI,qCAAqC,CAAC,SAAS,SAAS,GAAY,uBAAuB;AACjI,iBAAO,QAAQ,MAAM,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC,GAAG,CAAC,WAAW,CAAC;AAAA,QACpG;AAAA,MACF;AAAA,MACA,cAAc,qBAAoC,cAA8C;AAC9F,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,gBAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,iBAAO,QAAQ,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,YAAY,KAAK,MAAM,CAAC,aAAa,YAAY,CAAC;AAAA,QAC1G,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,cAAc,KAAK,eAAe;AACxC,iBAAO,QAAQ,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,KAAK,MAAM,CAAC,aAAa,GAAG,CAAC;AAAA,QACxF;AAAA,MACF;AAAA,MACA,MAAM,cAAc,qBAAoC,cAAuD;AAC7G,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,gBAAM,cAAc,MAAM,KAAK,gBAAgB,KAAK;AACpD,iBAAO,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,YAAY,KAAK;AAAA,QAC3D,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,cAAc,MAAM,KAAK,gBAAgB;AAC/C,iBAAO,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,KAAK;AAAA,QAClD;AAAA,MACF;AAAA,MACA,MAAM,cAAc,qBAAoC,cAAyC;AAC/F,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,iBAAQ,MAAM,KAAK,cAAc,OAAO,YAAsB,MAAO;AAAA,QACvE,OAAO;AACL,gBAAM,MAAM;AACZ,iBAAQ,MAAM,KAAK,cAAc,GAAG,MAAO;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,MAAM,OAAO,QAAQ;AACnB,eAAO,MAAM,IAAI,kBAAkB,QAAQ,OAAO;AAAA,MACpD;AAAA,MACA,MAAM,sBAAsB,SAAoC;AAC9D,YAAI,CAAC,KAAK,eAAe;AACvB,gBAAM,IAAI,oBAAoB,oCAAoC;AAAA,QACpE;AACA,eAAO,MAAM,IAAI,WAAW;AAAA,UAC1B,KAAK;AAAA,UACL,SAAS,eAAe,qBAAqB,IAAI,KAAK,mBAAmB,aAAa;AAAA,UACtF;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,eAAe,SAAsD;AACzE,cAAM,SAAS,MAAM,IAAI,WAAW,eAAe,SAAS,OAAO;AACnE,cAAM,IAAI,kBAAkB,QAAQ,CAAC,OAAO,CAAC;AAC7C,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY,SAA+B;AAC/C,cAAM,SAAS,MAAM,IAAI,WAAW,YAAY,SAAS,OAAO;AAChE,cAAM,IAAI,kBAAkB,QAAQ,CAAC,OAAO,CAAC;AAC7C,eAAO;AAAA,MACT;AAAA,MACA,cAAc,KAAK,iBAAiB,KAAK,oBAAoB,KAAK,eAAe,OAAO;AAAA,MACxF,MAAM,eAAe,MAAY;AAC/B,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,6BAA6B,UAAU,CAAC,SAAS,KAAK,EAAE,GAAG,YAAY,CAAC;AAChH,eAAO,IAAI,6BAA6B,QAAQ,OAAO;AAAA,MACzD;AAAA,MACA,eAAe,MAAY;AACzB,cAAM,SAAS,cAAc,IAAI,8BAA8B,CAAC,SAAS,KAAK,EAAE,GAAY,uBAAuB;AACnH,eAAO,IAAI,6BAA6B,QAAQ,OAAO;AAAA,MACzD;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,kBAAkB,OAAO;AAC9C,gBAAQ,YAAY;AAAA,MACtB;AAAA,MACA,MAAM,sBAAsB;AAC1B,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,4BAA4B,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AACtG,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8BA,OAAM,OAAO,CAAC;AAAA,MAC9E;AAAA,MACA,qBAAqB;AACnB,cAAM,SAAS,cAAc,IAAI,6BAA6B,CAAC,OAAO,GAAY,2BAA2B;AAC7G,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8BA,OAAM,OAAO,CAAC;AAAA,MAC9E;AAAA,MACA,MAAM,qBAAqB,MAAmC;AAC5D,cAAMA,QAAO,MAAM,IAAI,WAAW,2BAA2B,kCAAkC,MAAM,IAAI,GAAG,OAAO;AACnH,cAAM,IAAI,4BAA4B,QAAQ,CAAC,OAAO,CAAC;AACvD,eAAO,IAAI,8BAA8BA,OAAM,OAAO;AAAA,MACxD;AAAA,MAEA,aAAa;AACX,cAAM,SAAS,cAAc,IAAI,mBAAmB,CAAC,OAAO,GAAY,mBAAmB;AAC3F,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,sBAAsB,SAASA,KAAI,CAAC;AAAA,MACtE;AAAA,MAEA,MAAM,cAAc;AAClB,cAAM,UAAU,MAAM,IAAI,WAAW,mBAAmB,EAAE,SAAS,KAAK,GAAG,SAAS,QAAQ;AAC5F,eAAO,QAAQ,IAAI,CAACA,UAAS,IAAI,sBAAsB,SAASA,KAAI,CAAC;AAAA,MACvE;AAAA,MAEA,MAAM,aAAa,SAAwC;AACzD,cAAM,SAAS,MAAM,IAAI,WAAW;AAAA,UAClC,MAAM,4BAA4B,QAAQ,MAAM,OAAO;AAAA,UACvD;AAAA,UACA;AAAA,QACF;AACA,cAAM,IAAI,kBAAkB,QAAQ,CAAC,OAAO,CAAC;AAC7C,eAAO,IAAI,sBAAsB,SAAS,MAAM;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EAEU,yBAAyB,SAA6C;AAC9E,UAAM,MAAM;AACZ,SAAK,uBAAuB;AAC5B,WAAO;AAAA,MACL,cAAc,YAAiE;AAC7E,eAAO,IAAI,eAAe,SAAS,UAAU;AAAA,MAC/C;AAAA,MACA,oBAAoB;AAClB,eAAO,IAAI,mBAAmB,OAAO;AAAA,MACvC;AAAA,MACA,mBAAmB;AACjB,eAAO,IAAI,kBAAkB,OAAO;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EAEU,qBAAqB,MAAsD,SAAyD;AAC5I,UAAM,cAAc;AAAA,MAClB,GAAG,KAAK,gBAAgB,IAAI;AAAA,MAC5B,GAAG,KAAK,YAAY,OAAO;AAAA,MAC3B,GAAG,KAAK,4BAA4B,MAAM,OAAO;AAAA,MACjD,GAAG,KAAK,mBAAmB,IAAI,KAAK,yBAAyB,OAAO,IAAI,CAAC;AAAA,IAC3E;AAEA,WAAO,OAAO,WAAW;AACzB,WAAO;AAAA,EACT;AAAA,EACU,uBAAuB,MAAqD;AACpF,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,QAAQ,KAAK;AAAA,MACb,WAAW,IAAI,KAAK,KAAK,UAAU;AAAA,MACnC,iBAAiB,KAAK;AAAA,MACtB,YAAY,KAAK,eAAe,IAAI,KAAK,KAAK,YAAY,IAAI;AAAA,MAC9D,kBAAkB,KAAK,sBAAsB;AAAA,MAC7C,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AAAA,EAEU,kBAAkB,cAAsB,SAAuE;AACvH,QAAI,CAAC,KAAK,gBAAgB,IAAI,CAAC,SAAS,YAAY,CAAC,GAAG;AACtD,WAAK,gBAAgB,IAAI,CAAC,SAAS,YAAY,GAAG,IAAK,+BAA8B,sBAAsB,MAAQ;AAAA,QACjH,SAAS,KAAK,WAAW,QAAQ,WAAW;AAAA,QAC5C,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,KAAK,gBAAgB,IAAI,CAAC,SAAS,YAAY,CAAC;AAAA,EACzD;AAAA,EAEA,IAAI,YAAuB;AACzB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EAEA,MAAgB,WAAW,KAA+B;AACxD,WAAO,WAAW,GAAG;AAAA,EACvB;AAAA,EAEA,IAAI,OAA8B;AAChC,WAAO,QAAQ,KAAK,WAAW;AAAA,EACjC;AAAA,EAEA,MAAgB,iBAAiB;AAC/B,QAAI,KAAK,oBAAoB,QAAQ;AACnC,aAAO;AAAA,IACT;AACA,WAAO,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,EACrC;AAAA,EAEA,MAAgB,YAAY,SAAmD;AAC7E,QAAI,KAAK,oBAAoB,QAAQ;AACnC;AAAA,IACF,WAAW,OAAO,KAAK,oBAAoB,YAAY,KAAK,gBAAgB,UAAU;AACpF,WAAK,gBAAgB,SAAS,QAAQ,IAAI,SAAS,CAAC;AAAA,IACtD,OAAO;AACL,UAAI,QAAQ,SAAS;AACnB,eAAO,SAAS,QAAQ,QAAQ,GAAG;AAAA,MACrC,OAAO;AACL,eAAO,SAAS,OAAO,QAAQ,GAAG;AAAA,MACpC;AAAA,IACF;AAEA,UAAM,KAAK,GAAI;AAAA,EACjB;AAAA,EAEA,cAAoC;AAClC,QAAI,OAAO,KAAK,oBAAoB,UAAU;AAC5C,aAAO,KAAK,gBAAgB,YAAY;AAAA,IAC1C,WAAW,KAAK,oBAAoB,UAAU;AAC5C,aAAO,CAAC,OAAe,OAAO,SAAS,OAAO,EAAE;AAAA,IAClD,OAAO;AACL,aAAO,CAAC,OAAe;AAAA,MAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,MAAgB,mBAAmB,KAAa,SAA6B;AAC3E,QAAI,CAAC,MAAM,KAAK,WAAW,GAAG,GAAG;AAC/B,YAAM,IAAI,MAAM,gBAAgB,GAAG,sCAAsC;AAAA,IAC3E;AACA,WAAO,MAAM,KAAK,YAAY,EAAE,KAAK,GAAG,QAAQ,CAAC;AAAA,EACnD;AAAA,EAEA,MAAgB,mBAAmB,aAAgC,SAA6B;AAC9F,QAAI,MAAM,KAAK,KAAK,WAAW;AAC/B,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,2BAA2B,WAAW,EAAE;AAAA,IAC1D;AAEA,QAAI,CAAC,SAAS,gBAAgB;AAC5B,UAAI,gBAAgB,iBAAiB,gBAAgB,eAAe;AAClE,YAAI,iBAAiB,OAAO,WAAW,aAAa;AAAA,QAEpD,OAAO;AACL,gBAAM,cAAc,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC9D,gBAAM,YAAY,IAAI,sBAAsB,KAAK;AAAA,QACnD;AAAA,MACF,WAAW,gBAAgB,YAAY,gBAAgB,UAAU;AAC/D,YAAI,iBAAiB,OAAO,WAAW,aAAa;AAAA,QAEpD,OAAO;AACL,gBAAM,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI;AAC/C,gBAAM,UAAU,IAAI,IAAI,KAAK,UAAU;AACvC,cAAI,WAAW,aAAa,IAAI,sBAAsB,GAAG;AACvD,oBAAQ,aAAa,IAAI,wBAAwB,WAAW,aAAa,IAAI,sBAAsB,CAAE;AAAA,UACvG,WAAW,WAAW,aAAa,QAAQ,YAAY,WAAW,SAAS,QAAQ,MAAM;AACvF,oBAAQ,aAAa,IAAI,wBAAwB,gBAAgB,UAAU,CAAC;AAAA,UAC9E;AACA,gBAAM,gBAAgB,OAAO;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAEA,UAAM,KAAK,mBAAmB,KAAK,OAAO;AAAA,EAC5C;AAAA,EAEA,MAAM,iBAAiB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,UAAU,OAAO;AAAA,EAAG;AAAA,EAC/G,MAAM,iBAAiB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,UAAU,OAAO;AAAA,EAAG;AAAA,EAC/G,MAAM,kBAAkB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,WAAW,OAAO;AAAA,EAAG;AAAA,EACjH,MAAM,4BAA4B,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,qBAAqB,OAAO;AAAA,EAAG;AAAA,EACrI,MAAM,wBAAwB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,iBAAiB,OAAO;AAAA,EAAG;AAAA,EAC7H,MAAM,yBAAyB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,kBAAkB,OAAO;AAAA,EAAG;AAAA,EAC/H,MAAM,eAAe,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,QAAQ,OAAO;AAAA,EAAG;AAAA,EAC3G,MAAM,wBAAwB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,iBAAiB,OAAO;AAAA,EAAG;AAAA,EAC7H,MAAM,4BAA4B,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,qBAAqB,OAAO;AAAA,EAAG;AAAA,EACrI,MAAM,sBAAsB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,eAAe,OAAO;AAAA,EAAG;AAAA,EACzH,MAAM,sBAAsB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,eAAe,OAAO;AAAA,EAAG;AAAA,EACzH,MAAM,uBAAuB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,gBAAgB,OAAO;AAAA,EAAG;AAAA,EAC3H,MAAM,0BAA0B,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,mBAAmB,OAAO;AAAA,EAAG;AAAA,EACjI,MAAM,gBAAgB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,SAAS,OAAO;AAAA,EAAG;AAAA,EAC7G,MAAM,yBAAyB,SAA6B;AAAE,WAAO,MAAM,KAAK,mBAAmB,kBAAkB,OAAO;AAAA,EAAG;AAAA,EAE/H,MAAM,wBAAwB,OAAe,SAA6F;AACxI,WAAO,MAAM,KAAK,WAAW,wBAAwB,OAAO,SAAS,eAAe,qBAAqB,KAAK,KAAK,eAAe,aAAa,CAAC;AAAA,EAClJ;AAAA,EAEA,MAAM,mBAAmB,OAAe,SAAkH;AACxJ,WAAO,MAAM,KAAK,WAAW,mBAAmB,OAAO,SAAS,eAAe,qBAAqB,KAAK,KAAK,mBAAmB,aAAa,CAAC;AAAA,EACjJ;AAAA,EAEA,MAAM,cAAc,SAA+G;AACjI,WAAO,MAAM,KAAK,WAAW,cAAc,OAAO;AAAA,EACpD;AAAA,EAEA,MAAM,wBAAwB,MAAgF;AAC5G,WAAO,MAAM,KAAK,WAAW,wBAAwB,IAAI;AAAA,EAC3D;AAAA,EAEA,MAAM,yBAAyB,MAAgF;AAC7G,WAAO,MAAM,KAAK,WAAW,qBAAqB;AAAA,MAChD,MAAM;AAAA,MACN;AAAA,MACA,SAAS,MAAM,KAAK,YAAY;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,qBAAqB,MAAgF;AACzG,UAAM,SAAS,MAAM,KAAK,WAAW,qBAAqB;AAAA,MACxD,MAAM;AAAA,MACN;AAAA,MACA,SAAS,MAAM,KAAK,YAAY;AAAA,IAClC,CAAC;AAED,QAAI,OAAO,WAAW,MAAM;AAC1B,aAAO,OAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,MAAM,yBAAyB,MAAkG;AAC/H,UAAM,SAAS,MAAM,KAAK,WAAW,qBAAqB;AAAA,MACxD,MAAM;AAAA,MACN;AAAA,MACA,SAAS,MAAM,KAAK,YAAY;AAAA,IAClC,CAAC;AAED,QAAI,OAAO,WAAW,MAAM;AAC1B,aAAO,OAAO,GAAG,EAAE,iBAAiB,OAAO,KAAK,kBAAkB,CAAC;AAAA,IACrE,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,MAAM,YAAY,MAAgF;AAChG,UAAM,SAAS,MAAM,KAAK,WAAW,YAAY,IAAI;AACrD,UAAM,KAAK,kBAAkB,QAAQ,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC;AAC/D,UAAM,KAAK,4BAA4B,QAAQ,CAAC,MAAM,KAAK,YAAY,CAAC,CAAC;AACzE,WAAO;AAAA,EACT;AAAA,EAMA,MAAM,QAAQ,SAAwF;AACpG,SAAK,4BAA4B,SAAS,UAAU;AACpD,UAAM,UAAU,MAAM,KAAK,YAAY,SAAS,UAAU;AAC1D,QAAI,OAAO,OAAO,QAAQ,MAAM,KAAK,kBAAkB,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AACzF,QAAI,MAAM,gBAAgB,SAAS,OAAO,eAAe,SAAS,OAAO,uBAAuB;AAC9F,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM;AACjB,cAAQ,SAAS,IAAI;AAAA,QACnB,KAAK,YAAY;AACf,gBAAM,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC7C;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,gBAAM,IAAI,MAAM,mEAAmE;AAAA,QACrF;AAAA,QACA,KAAK,aAAa;AAChB,gBAAM,SAAS,MAAM,KAAK,mBAAmB;AAC7C,iBAAO,MAAM,KAAK,QAAQ,EAAE,YAAY,QAAQ,IAAI,sBAAsB,CAAC,KAAK,SAAS,mDAAmD;AAAA,QAC9I;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,eAAe;AAClB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,WAAO,QAAQ,KAAK,qBAAqB,MAAM,OAAO;AAAA,EACxD;AAAA,EAMA,QAAQ,SAA+E;AACrF,SAAK,4BAA4B,SAAS,UAAU;AAEpD,UAAM,UAAU,KAAK,YAAY,SAAS,UAAU;AACpD,QAAI,OAAO,cAAc,KAAK,mBAAmB,CAAC,OAAO,GAAY,WAAW;AAChF,QAAI,MAAM,gBAAgB,SAAS,OAAO,eAAe,SAAS,OAAO,uBAAuB;AAC9F,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,MAAM;AACjB,cAAQ,SAAS,IAAI;AAAA,QACnB,KAAK,YAAY;AACf,4BAAkB,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC,CAAC;AAC1D,kBAAQ;AACR,gBAAM,IAAI,oBAAoB,6BAA6B;AAAA,QAC7D;AAAA,QACA,KAAK,SAAS;AACZ,gBAAM,IAAI,MAAM,mEAAmE;AAAA,QACrF;AAAA,QACA,KAAK,aAAa;AAGhB,4BAAkB,YAAY;AAC5B,kBAAM,KAAK,mBAAmB;AAC9B,gBAAI,OAAO,WAAW,aAAa;AACjC,qBAAO,SAAS,OAAO;AAAA,YACzB;AAAA,UACF,CAAC;AACD,kBAAQ;AACR,gBAAM,IAAI,oBAAoB,6BAA6B;AAAA,QAC7D;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,eAAe;AAAA,QAEpB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,QAAQ,MAAM;AACnB,aAAO,QAAQ,KAAK,qBAAqB,MAAM,OAAO;AAAA,IACxD,GAAG,CAAC,MAAM,SAAS,SAAS,EAAE,CAAC;AAAA,EACjC;AAAA,EAEA,MAAgB,kBAAkB,QAA2B,SAA0B;AACrF,UAAM,MAAM,MAAM,KAAK,WAAW,iBAAiB,wBAAwB,MAAM,GAAG,OAAO;AAC3F,UAAM,KAAK,aAAa,OAAO;AAC/B,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,gBAAgB,UAAwB;AAC5C,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAEA,SAAK,4BAA4B;AACjC,UAAM;AAAA,MACJ,KAAK;AAAA,MAAY;AAAA,QACf;AAAA,QACA,aAAa,KAAK,KAAK;AAAA,QACvB,kBAAkB,KAAK,KAAK;AAAA,QAC5B,eAAe,KAAK,qBAAqB,QAAQ,GAAG,KAAK,GAAG;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAgB,iBAAiB,OAAyD,SAA0B;AAClH,UAAM,MAAM,OAAO,mEAAmE;AACtF,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,YAAY,gBAAgB;AAAA,IACxC;AAEA,WAAO,MAAM,KAAK,WAAW;AAAA,MAC1B,MAAM,SAAiB,gBAAgB,SAAS,sBAAsB;AAAA,MACvE;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAgB,uBAA6B,UAAgI;AAC3K,QAAI;AACF,aAAO,MAAM,SAAS;AAAA,IACxB,SAAS,GAAG;AACV,UAAI,YAAY,kCAAkC,WAAW,CAAC,GAAG;AAC/D,eAAO,OAAO,GAAG,MAAM,KAAK,iBAAiB,GAAG,MAAM,KAAK,YAAY,CAAC,CAAC;AAAA,MAC3E;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB,SAI2E;AACpG,SAAK,4BAA4B;AACjC,UAAM,UAAU,MAAM,KAAK,YAAY;AACvC,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,KAAK,uBAAuB,YAAY;AACrD,eAAO,MAAM,KAAK,WAAW,qBAAqB,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,MAC5F,CAAC;AAAA,IACH,SAAS,GAAG;AACV,UAAI,YAAY,gBAAgB,WAAW,CAAC,GAAG;AAC7C,eAAO,OAAO,MAAM,CAAC;AAAA,MACvB;AACA,YAAM;AAAA,IACR;AAEA,QAAI,OAAO,WAAW,MAAM;AAC1B,YAAM,KAAK,2BAA2B,OAAO,IAAI;AACjD,UAAI,CAAC,QAAQ,YAAY;AACvB,cAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,MACpD;AACA,aAAO,OAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB,SAK2F;AACpH,SAAK,4BAA4B;AACjC,UAAM,UAAU,MAAM,KAAK,YAAY;AACvC,UAAM,+BAA+B,QAAQ,2BAA2B,qBAAqB,KAAK,KAAK,mBAAmB,yBAAyB;AACnJ,UAAM,SAAS,MAAM,KAAK,WAAW;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACF;AACA,QAAI,OAAO,WAAW,MAAM;AAC1B,YAAM,KAAK,2BAA2B,OAAO,IAAI;AACjD,UAAI,CAAC,QAAQ,YAAY;AACvB,cAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,MACpD;AACA,aAAO,OAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,MAAM,qBAAqB;AACzB,SAAK,4BAA4B;AAEjC,QAAI,CAAC,KAAK,4BAA4B;AACpC,WAAK,8BAA8B,YAAY;AAC7C,aAAK,4BAA4B;AACjC,cAAM,UAAU,MAAM,KAAK,YAAY;AACvC,cAAM,SAAS,MAAM,KAAK,WAAW,kBAAkB,OAAO;AAC9D,YAAI,OAAO,WAAW,MAAM;AAC1B,gBAAM,KAAK,2BAA2B,OAAO,IAAI;AAAA,QACnD,OAAO;AACL,gBAAM,IAAI,oBAAoB,kDAAkD;AAAA,QAClF;AACA,aAAK,6BAA6B;AAClC,eAAO,OAAO;AAAA,MAChB,GAAG;AAAA,IACL;AAEA,WAAO,MAAM,KAAK;AAAA,EACpB;AAAA,EAEA,MAAM,oBAAoB,MAAc,SAAuI;AAC7K,SAAK,4BAA4B;AACjC,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,KAAK,uBAAuB,YAAY;AACrD,eAAO,MAAM,KAAK,WAAW,oBAAoB,IAAI;AAAA,MACvD,CAAC;AAAA,IACH,SAAS,GAAG;AACV,UAAI,YAAY,gBAAgB,WAAW,CAAC,GAAG;AAC7C,eAAO,OAAO,MAAM,CAAC;AAAA,MACvB;AACA,YAAM;AAAA,IACR;AAEA,QAAI,OAAO,WAAW,MAAM;AAC1B,YAAM,KAAK,2BAA2B,OAAO,IAAI;AACjD,UAAI,CAAE,SAAS,YAAa;AAC1B,YAAI,OAAO,KAAK,SAAS;AACvB,gBAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,QACpD,OAAO;AACL,gBAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAAA,QACpD;AAAA,MACF;AACA,aAAO,OAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,eAAe,SAM2G;AAE9H,UAAM,WAAW,MAAM,KAAK,WAAW;AAAA,MACrC;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,mBAAmB,QAAQ;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,IAAI;AAChB,aAAO,OAAO,MAAM,IAAI,YAAY,aAAa,gCAAgC,SAAS,MAAM,IAAI,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC;AAAA,IAC9H;AAEA,UAAM,aAAa,MAAM,SAAS,KAAK;AACvC,UAAM,cAAc,WAAW;AAC/B,UAAM,YAAY,WAAW;AAG7B,UAAM,MAAM,GAAG,QAAQ,MAAM,wCAAwC,mBAAmB,SAAS,CAAC;AAClG,QAAI,QAAQ,YAAY;AACtB,cAAQ,WAAW,GAAG;AAAA,IACxB,OAAO;AACL,cAAQ,IAAI;AAAA,EAAoD,GAAG,EAAE;AAAA,IACvE;AAIA,QAAI,WAAW;AACf,WAAO,YAAY,QAAQ,eAAe,WAAW;AACnD;AACA,YAAM,eAAe,MAAM,KAAK,WAAW,kBAAkB,kBAAkB;AAAA,QAC7E,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,GAAG,IAAI;AAEP,UAAI,CAAC,aAAa,IAAI;AACpB,eAAO,OAAO,MAAM,IAAI,YAAY,aAAa,gCAAgC,aAAa,MAAM,IAAI,MAAM,aAAa,KAAK,CAAC,EAAE,CAAC;AAAA,MACtI;AACA,YAAM,aAAa,MAAM,aAAa,KAAK;AAE3C,UAAI,aAAa,WAAW,OAAO,WAAW,WAAW,WAAW;AAClE,eAAO,OAAO,GAAG,WAAW,aAAa;AAAA,MAC3C;AACA,UAAI,WAAW,WAAW,WAAW;AACnC,cAAM,KAAK,QAAQ,kBAAkB,GAAI;AACzC;AAAA,MACF;AACA,UAAI,WAAW,WAAW,WAAW;AACnC,eAAO,OAAO,MAAM,IAAI,YAAY,oBAAoB,uDAAuD,CAAC;AAAA,MAClH;AACA,UAAI,WAAW,WAAW,QAAQ;AAChC,eAAO,OAAO,MAAM,IAAI,YAAY,iBAAiB,kDAAkD,CAAC;AAAA,MAC1G;AACA,aAAO,OAAO,MAAM,IAAI,YAAY,aAAa,4CAA4C,WAAW,MAAM,EAAE,CAAC;AAAA,IACnH;AAEA,WAAO,OAAO,MAAM,IAAI,YAAY,aAAa,2CAA2C,CAAC;AAAA,EAC/F;AAAA,EAEA,MAAM,oBAAmK;AACvK,SAAK,4BAA4B;AACjC,UAAM,UAAU,MAAM,KAAK,YAAY;AACvC,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,KAAK,uBAAuB,YAAY;AACrD,cAAM,mBAAmB,MAAM,KAAK,WAAW,8BAA8B,CAAC,GAAG,OAAO;AACxF,YAAI,iBAAiB,WAAW,MAAM;AACpC,iBAAO,OAAO,MAAM,IAAI,YAAY,4BAA4B,6DAA6D,CAAC;AAAA,QAChI;AAEA,cAAM,EAAE,cAAc,KAAK,IAAI,iBAAiB;AAGhD,YAAI,aAAa,SAAS,2CAA2C;AACnE,gBAAM,IAAI,oBAAoB,oEAAoE,aAAa,IAAI,EAAE;AAAA,QACvH;AACA,qBAAa,OAAO,OAAO,SAAS;AAEpC,cAAM,0BAA0B,MAAM,oBAAoB,EAAE,aAAa,aAAa,CAAC;AACvF,eAAO,MAAM,KAAK,WAAW,kBAAkB,EAAE,yBAAyB,KAAK,CAAC;AAAA,MAClF,CAAC;AAAA,IACH,SAAS,OAAO;AACd,UAAI,iBAAiB,eAAe;AAClC,eAAO,OAAO,MAAM,IAAI,YAAY,qBAAqB,MAAM,SAAS,MAAM,IAAI,CAAC;AAAA,MACrF,OAAO;AAEL,eAAO,OAAO,MAAM,IAAI,YAAY,4BAA4B,gCAAgC,CAAC;AAAA,MACnG;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,MAAM;AAC1B,YAAM,KAAK,2BAA2B,OAAO,IAAI;AACjD,YAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAClD,aAAO,OAAO,GAAG,MAAS;AAAA,IAC5B,OAAO;AACL,aAAO,OAAO,MAAM,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAGA,MAAM,oBAAoB;AACxB,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM,IAAI,MAAM,yEAAyE;AAAA,IAC3F;AACA,SAAK,4BAA4B;AACjC,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,KAAK,uBAAuB,YAAY;AACrD,eAAO,MAAM,kBAAkB,KAAK,YAAY,KAAK,KAAK,aAAa;AAAA,MACzE,CAAC;AAAA,IACH,SAAS,GAAG;AACV,UAAI,YAAY,gBAAgB,WAAW,CAAC,GAAG;AAC7C,cAAM,iDAAiD;AACvD,eAAO;AAAA,MACT,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,OAAO,WAAW,QAAQ,OAAO,MAAM;AACzC,YAAM,KAAK,2BAA2B,OAAO,IAAI;AAGjD,UAAI,8BAA8B,OAAO,QAAQ,OAAO,KAAK,0BAA0B;AACrF,cAAM,KAAK,YAAY,EAAE,KAAK,OAAO,KAAK,0BAA0B,SAAS,KAAK,CAAC;AACnF,eAAO;AAAA,MACT,WAAW,OAAO,KAAK,SAAS;AAC9B,cAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAClD,eAAO;AAAA,MACT,OAAO;AACL,cAAM,KAAK,sBAAsB,EAAE,SAAS,KAAK,CAAC;AAClD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAgB,SAAS,SAA0B,SAAyD;AAC1G,UAAM,UAAU,cAAc,YAAY;AACxC,YAAM,KAAK,WAAW,QAAQ,OAAO;AACrC,UAAI,SAAS,aAAa;AACxB,cAAM,KAAK,YAAY,EAAE,KAAK,QAAQ,aAAa,SAAS,KAAK,CAAC;AAAA,MACpE,OAAO;AACL,cAAM,KAAK,uBAAuB;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,QAAQ,SAAyD;AACrE,UAAM,OAAO,MAAM,KAAK,QAAQ;AAChC,QAAI,MAAM;AACR,YAAM,KAAK,QAAQ,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,aAA+B;AACnC,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,qBAAqB,UAAU,CAAC,GAAG,YAAY,CAAC;AACvF,WAAO,KAAK,uBAAuB,IAAI;AAAA,EACzC;AAAA,EAEA,aAAsB;AACpB,UAAM,OAAO,cAAc,KAAK,sBAAsB,CAAC,GAAG,cAAc;AACxE,WAAO,QAAQ,MAAM,KAAK,uBAAuB,IAAI,GAAG,CAAC,IAAI,CAAC;AAAA,EAChE;AAAA,EAEA,MAAgB,mBAAmB,SAAwD;AACzF,SAAK,uBAAuB;AAC5B,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,oBAAoB,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AAC7F,WAAO,KAAK,IAAI,CAAC,MAAM,KAAK,kBAAkB,EAAE,IAAI,OAAO,EAAE;AAAA,MAC3D;AAAA,MACA,MAAM,KAAK,sBAAsB,OAAO;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEU,kBAAkB,SAA+C;AACzE,SAAK,uBAAuB;AAC5B,UAAM,WAAW,cAAc,KAAK,qBAAqB,CAAC,OAAO,GAAG,oBAAoB;AACxF,WAAO,QAAQ,MAAM,SAAS,IAAI,CAAC,MAAM,KAAK,kBAAkB,EAAE,IAAI,OAAO,EAAE;AAAA,MAC7E;AAAA,MACA,MAAM,KAAK,sBAAsB,OAAO;AAAA,IAC1C,CAAC,GAAG,CAAC,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,MAAgB,eAAe,SAA0B,YAA6F;AACpJ,SAAK,uBAAuB;AAC5B,UAAM,OAAO,MAAM,KAAK,WAAW,cAAc,gCAAgC,UAAU,GAAG,OAAO;AACrG,UAAM,MAAM,KAAK,kBAAkB,KAAK,IAAI,OAAO,EAAE;AAAA,MACnD;AAAA,MACA,MAAM,KAAK,sBAAsB,OAAO;AAAA,IAC1C;AACA,UAAM,KAAK,sBAAsB,OAAO;AACxC,WAAO;AAAA,EACT;AAAA,EAEA,MAAgB,aAAa,SAA0B;AAErD,UAAM,KAAK,gBAAgB,OAAO;AAAA,EACpC;AAAA,EAEA,MAAgB,gBAAgB,SAA0B;AACxD,UAAM,KAAK,kBAAkB,QAAQ,CAAC,OAAO,CAAC;AAAA,EAChD;AAAA,EAEA,MAAgB,gBAAgB;AAAA,EAEhC;AAAA,EAEA,MAAgB,kBAAkB;AAChC,UAAM,KAAK,qBAAqB,QAAQ,CAAC,CAAC;AAAA,EAC5C;AAAA,EAEA,MAAgB,sBAAsB,SAA0B;AAC9D,UAAM,KAAK,oBAAoB,QAAQ,CAAC,OAAO,CAAC;AAAA,EAClD;AAAA,EAEA,YAAY,uBAAuB,IAAI;AACrC,WAAO;AAAA,MACL,gBAAgB,CACd,SAC6C;AAC7C,cAAM,sBAAsB,KAAK,UAAU,KAAK,MAAM;AAAA;AAAA,QAAqB,CAAC,CAAC;AAC7E,cAAM,WAAW,cAAc,IAAI,KAAK,gBAAgB;AACxD,YAAI,UAAU;AACZ,gBAAM,CAAC,qBAAqB,SAAS,IAAI;AACzC,cAAI,wBAAwB,UAAa,wBAAwB,qBAAqB;AACpF,kBAAM,IAAI,oBAAoB,qHAAqH,EAAE,aAAa,MAAM,gBAAgB,oBAAoB,CAAC;AAAA,UAC/M;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,IAAI,+BAAwD;AAAA,UACjE,GAAG;AAAA,UACH,aAAa;AAAA,QACf,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EAEA,KAAK,uBAAuB,IAAI;AAC9B,WAAO;AAAA,MACL,cAAc,MAAoD;AAChE,YAAI,EAAE,0BAA0B,KAAK,WAAW,UAAU;AAExD,gBAAM,IAAI,oBAAoB,+EAA+E;AAAA,QAC/G;AAEA,YAAI,OAAO,KAAK,oBAAoB,UAAU;AAC5C,gBAAM,IAAI,oBAAoB,gFAAgF;AAAA,QAChH;AAEA,eAAO;AAAA,UACL,SAAS,KAAK,SAAS;AAAA,UACvB,WAAW,KAAK;AAAA,UAChB,sBAAsB,KAAK,WAAW,QAAQ;AAAA,UAC9C,YAAY,KAAK;AAAA,UACjB,MAAM,KAAK;AAAA,UACX,qBAAqB,KAAK;AAAA,UAC1B,kBAAkB,KAAK,qBAAqB;AAAA,UAC5C,gBAAgB,KAAK;AAAA,UACrB,qBAAqB,KAAK,SAAS;AAAA,QACrC;AAAA,MACF;AAAA,MACA,gBAAgB,CAAC,oBAAuE;AACtF,0BAAkB,YAAY;AAC5B,gBAAM,KAAK,kBAAkB,yBAAyB,CAAC,MAAM,KAAK,YAAY,CAAC,GAAG,OAAO,YAAY,eAAe,CAAC;AAAA,QACvH,CAAC;AAAA,MACH;AAAA,MACA,aAAa,OACX,MACA,gBACA,cAA6C,aAC1C;AACH,eAAO,MAAM,KAAK,WAAW,kBAAkB,MAAM,gBAAgB,MAAM,KAAK,YAAY,GAAG,WAAW;AAAA,MAC5G;AAAA,IACF;AAAA,EACF;AACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5uDa,+BAQK,wBAA+G,EAAE,OAAO,OAAU;AAR7I,IAAM,gCAAN;", "names": ["parsed", "crud"]}