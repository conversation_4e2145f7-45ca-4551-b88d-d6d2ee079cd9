{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/implementations/server-app-impl.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\nimport { KnownErrors, StackServerInterface } from \"@stackframe/stack-shared\";\nimport { ContactChannelsCrud } from \"@stackframe/stack-shared/dist/interface/crud/contact-channels\";\nimport { TeamApiKeysCrud, UserApiKeysCrud, teamApiKeysCreateOutputSchema, userApiKeysCreateOutputSchema } from \"@stackframe/stack-shared/dist/interface/crud/project-api-keys\";\nimport { ProjectPermissionDefinitionsCrud, ProjectPermissionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/project-permissions\";\nimport { TeamInvitationCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-invitation\";\nimport { TeamMemberProfilesCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-member-profiles\";\nimport { TeamPermissionDefinitionsCrud, TeamPermissionsCrud } from \"@stackframe/stack-shared/dist/interface/crud/team-permissions\";\nimport { TeamsCrud } from \"@stackframe/stack-shared/dist/interface/crud/teams\";\nimport { UsersCrud } from \"@stackframe/stack-shared/dist/interface/crud/users\";\nimport { InternalSession } from \"@stackframe/stack-shared/dist/sessions\";\nimport { StackAssertionError, throwErr } from \"@stackframe/stack-shared/dist/utils/errors\";\nimport { ProviderType } from \"@stackframe/stack-shared/dist/utils/oauth\";\nimport { runAsynchronously } from \"@stackframe/stack-shared/dist/utils/promises\";\nimport { suspend } from \"@stackframe/stack-shared/dist/utils/react\";\nimport { Result } from \"@stackframe/stack-shared/dist/utils/results\";\nimport { useMemo } from \"react\"; // THIS_LINE_PLATFORM react-like\nimport * as yup from \"yup\";\nimport { constructRedirectUrl } from \"../../../../utils/url\";\nimport { ApiKey, ApiKeyCreationOptions, ApiKeyUpdateOptions, apiKeyCreationOptionsToCrud, apiKeyUpdateOptionsToCrud } from \"../../api-keys\";\nimport { GetUserOptions, HandlerUrls, OAuthScopesOnSignIn, TokenStoreInit } from \"../../common\";\nimport { OAuthConnection } from \"../../connected-accounts\";\nimport { ServerContactChannel, ServerContactChannelCreateOptions, ServerContactChannelUpdateOptions, serverContactChannelCreateOptionsToCrud, serverContactChannelUpdateOptionsToCrud } from \"../../contact-channels\";\nimport { AdminProjectPermissionDefinition, AdminTeamPermission, AdminTeamPermissionDefinition } from \"../../permissions\";\nimport { EditableTeamMemberProfile, ServerListUsersOptions, ServerTeam, ServerTeamCreateOptions, ServerTeamUpdateOptions, ServerTeamUser, Team, TeamInvitation, serverTeamCreateOptionsToCrud, serverTeamUpdateOptionsToCrud } from \"../../teams\";\nimport { ProjectCurrentServerUser, ServerUser, ServerUserCreateOptions, ServerUserUpdateOptions, serverUserCreateOptionsToCrud, serverUserUpdateOptionsToCrud } from \"../../users\";\nimport { StackServerAppConstructorOptions } from \"../interfaces/server-app\";\nimport { _StackClientAppImplIncomplete } from \"./client-app-impl\";\nimport { clientVersion, createCache, createCacheBySession, getBaseUrl, getDefaultProjectId, getDefaultPublishableClientKey, getDefaultSecretServerKey } from \"./common\";\n\nimport { useAsyncCache } from \"./common\";\n\nexport class _StackServerAppImplIncomplete<HasTokenStore extends boolean, ProjectId extends string> extends _StackClientAppImplIncomplete<HasTokenStore, ProjectId>\n{\n  declare protected _interface: StackServerInterface;\n\n  // TODO override the client user cache to use the server user cache, so we save some requests\n  private readonly _currentServerUserCache = createCacheBySession(async (session) => {\n    if (session.isKnownToBeInvalid()) {\n      // see comment in _currentUserCache for more details on why we do this\n      return null;\n    }\n    return await this._interface.getServerUserByToken(session);\n  });\n  private readonly _serverUsersCache = createCache<[\n    cursor?: string,\n    limit?: number,\n    orderBy?: 'signedUpAt',\n    desc?: boolean,\n    query?: string,\n  ], UsersCrud['Server']['List']>(async ([cursor, limit, orderBy, desc, query]) => {\n    return await this._interface.listServerUsers({ cursor, limit, orderBy, desc, query });\n  });\n  private readonly _serverUserCache = createCache<string[], UsersCrud['Server']['Read'] | null>(async ([userId]) => {\n    const user = await this._interface.getServerUserById(userId);\n    return Result.or(user, null);\n  });\n  private readonly _serverTeamsCache = createCache<[string | undefined], TeamsCrud['Server']['Read'][]>(async ([userId]) => {\n    return await this._interface.listServerTeams({ userId });\n  });\n  private readonly _serverTeamUserPermissionsCache = createCache<\n    [string, string, boolean],\n    TeamPermissionsCrud['Server']['Read'][]\n  >(async ([teamId, userId, recursive]) => {\n    return await this._interface.listServerTeamPermissions({ teamId, userId, recursive }, null);\n  });\n  private readonly _serverUserProjectPermissionsCache = createCache<\n    [string, boolean],\n    ProjectPermissionsCrud['Server']['Read'][]\n  >(async ([userId, recursive]) => {\n    return await this._interface.listServerProjectPermissions({ userId, recursive }, null);\n  });\n  private readonly _serverUserOAuthConnectionAccessTokensCache = createCache<[string, string, string], { accessToken: string } | null>(\n    async ([userId, providerId, scope]) => {\n      try {\n        const result = await this._interface.createServerProviderAccessToken(userId, providerId, scope || \"\");\n        return { accessToken: result.access_token };\n      } catch (err) {\n        if (!(KnownErrors.OAuthConnectionDoesNotHaveRequiredScope.isInstance(err) || KnownErrors.OAuthConnectionNotConnectedToUser.isInstance(err))) {\n          throw err;\n        }\n      }\n      return null;\n    }\n  );\n  private readonly _serverUserOAuthConnectionCache = createCache<[string, ProviderType, string, boolean], OAuthConnection | null>(\n    async ([userId, providerId, scope, redirect]) => {\n      return await this._getUserOAuthConnectionCacheFn({\n        getUser: async () => Result.orThrow(await this._serverUserCache.getOrWait([userId], \"write-only\")),\n        getOrWaitOAuthToken: async () => Result.orThrow(await this._serverUserOAuthConnectionAccessTokensCache.getOrWait([userId, providerId, scope || \"\"] as const, \"write-only\")),\n        useOAuthToken: () => useAsyncCache(this._serverUserOAuthConnectionAccessTokensCache, [userId, providerId, scope || \"\"] as const, \"user.useConnectedAccount()\"),\n        providerId,\n        scope,\n        redirect,\n        session: null,\n      });\n    }\n  );\n  private readonly _serverTeamMemberProfilesCache = createCache<[string], TeamMemberProfilesCrud['Server']['Read'][]>(\n    async ([teamId]) => {\n      return await this._interface.listServerTeamMemberProfiles({ teamId });\n    }\n  );\n  private readonly _serverTeamInvitationsCache = createCache<[string], TeamInvitationCrud['Server']['Read'][]>(\n    async ([teamId]) => {\n      return await this._interface.listServerTeamInvitations({ teamId });\n    }\n  );\n  private readonly _serverUserTeamProfileCache = createCache<[string, string], TeamMemberProfilesCrud['Client']['Read']>(\n    async ([teamId, userId]) => {\n      return await this._interface.getServerTeamMemberProfile({ teamId, userId });\n    }\n  );\n  private readonly _serverContactChannelsCache = createCache<[string], ContactChannelsCrud['Server']['Read'][]>(\n    async ([userId]) => {\n      return await this._interface.listServerContactChannels(userId);\n    }\n  );\n\n  private readonly _serverUserApiKeysCache = createCache<[string], UserApiKeysCrud['Server']['Read'][]>(\n    async ([userId]) => {\n      const result = await this._interface.listProjectApiKeys({\n        user_id: userId,\n      }, null, \"server\");\n      return result as UserApiKeysCrud['Server']['Read'][];\n    }\n  );\n\n  private readonly _serverTeamApiKeysCache = createCache<[string], TeamApiKeysCrud['Server']['Read'][]>(\n    async ([teamId]) => {\n      const result = await this._interface.listProjectApiKeys({\n        team_id: teamId,\n      }, null, \"server\");\n      return result as TeamApiKeysCrud['Server']['Read'][];\n    }\n  );\n\n  private readonly _serverCheckApiKeyCache = createCache<[\"user\" | \"team\", string], UserApiKeysCrud['Server']['Read'] | TeamApiKeysCrud['Server']['Read'] | null>(async ([type, apiKey]) => {\n    const result = await this._interface.checkProjectApiKey(\n      type,\n      apiKey,\n      null,\n      \"server\",\n    );\n    return result;\n  });\n\n  private async _updateServerUser(userId: string, update: ServerUserUpdateOptions): Promise<UsersCrud['Server']['Read']> {\n    const result = await this._interface.updateServerUser(userId, serverUserUpdateOptionsToCrud(update));\n    await this._refreshUsers();\n    return result;\n  }\n\n  protected _serverEditableTeamProfileFromCrud(crud: TeamMemberProfilesCrud['Client']['Read']): EditableTeamMemberProfile {\n    const app = this;\n    return {\n      displayName: crud.display_name,\n      profileImageUrl: crud.profile_image_url,\n      async update(update: { displayName?: string, profileImageUrl?: string }) {\n        await app._interface.updateServerTeamMemberProfile({\n          teamId: crud.team_id,\n          userId: crud.user_id,\n          profile: {\n            display_name: update.displayName,\n            profile_image_url: update.profileImageUrl,\n          },\n        });\n        await app._serverUserTeamProfileCache.refresh([crud.team_id, crud.user_id]);\n      }\n    };\n  }\n\n  protected _serverContactChannelFromCrud(userId: string, crud: ContactChannelsCrud['Server']['Read']): ServerContactChannel {\n    const app = this;\n    return {\n      id: crud.id,\n      value: crud.value,\n      type: crud.type,\n      isVerified: crud.is_verified,\n      isPrimary: crud.is_primary,\n      usedForAuth: crud.used_for_auth,\n      async sendVerificationEmail(options?: { callbackUrl?: string }) {\n        await app._interface.sendServerContactChannelVerificationEmail(userId, crud.id, options?.callbackUrl ?? constructRedirectUrl(app.urls.emailVerification, \"callbackUrl\"));\n      },\n      async update(data: ServerContactChannelUpdateOptions) {\n        await app._interface.updateServerContactChannel(userId, crud.id, serverContactChannelUpdateOptionsToCrud(data));\n        await Promise.all([\n          app._serverContactChannelsCache.refresh([userId]),\n          app._serverUserCache.refresh([userId])\n        ]);\n      },\n      async delete() {\n        await app._interface.deleteServerContactChannel(userId, crud.id);\n        await Promise.all([\n          app._serverContactChannelsCache.refresh([userId]),\n          app._serverUserCache.refresh([userId])\n        ]);\n      },\n    };\n  }\n\n  constructor(options:\n    | StackServerAppConstructorOptions<HasTokenStore, ProjectId>\n    | {\n      interface: StackServerInterface,\n      tokenStore: TokenStoreInit<HasTokenStore>,\n      urls: Partial<HandlerUrls> | undefined,\n      oauthScopesOnSignIn?: Partial<OAuthScopesOnSignIn> | undefined,\n    }\n  ) {\n    super(\"interface\" in options ? {\n      interface: options.interface,\n      tokenStore: options.tokenStore,\n      urls: options.urls,\n      oauthScopesOnSignIn: options.oauthScopesOnSignIn,\n    } : {\n      interface: new StackServerInterface({\n        getBaseUrl: () => getBaseUrl(options.baseUrl),\n        projectId: options.projectId ?? getDefaultProjectId(),\n        extraRequestHeaders: options.extraRequestHeaders ?? {},\n        clientVersion,\n        publishableClientKey: options.publishableClientKey ?? getDefaultPublishableClientKey(),\n        secretServerKey: options.secretServerKey ?? getDefaultSecretServerKey(),\n      }),\n      baseUrl: options.baseUrl,\n      extraRequestHeaders: options.extraRequestHeaders,\n      projectId: options.projectId,\n      publishableClientKey: options.publishableClientKey,\n      tokenStore: options.tokenStore,\n      urls: options.urls,\n      oauthScopesOnSignIn: options.oauthScopesOnSignIn,\n      redirectMethod: options.redirectMethod,\n    });\n  }\n\n\n  protected _serverApiKeyFromCrud(crud: TeamApiKeysCrud['Client']['Read']): ApiKey<\"team\">;\n  protected _serverApiKeyFromCrud(crud: UserApiKeysCrud['Client']['Read']): ApiKey<\"user\">;\n  protected _serverApiKeyFromCrud(crud: yup.InferType<typeof teamApiKeysCreateOutputSchema>): ApiKey<\"team\", true>;\n  protected _serverApiKeyFromCrud(crud: yup.InferType<typeof userApiKeysCreateOutputSchema>): ApiKey<\"user\", true>;\n  protected _serverApiKeyFromCrud(crud: TeamApiKeysCrud['Client']['Read'] | UserApiKeysCrud['Client']['Read'] | yup.InferType<typeof teamApiKeysCreateOutputSchema> | yup.InferType<typeof userApiKeysCreateOutputSchema>): ApiKey<\"user\" | \"team\", boolean> {\n    return {\n      ...this._baseApiKeyFromCrud(crud),\n      async revoke() {\n        await this.update({ revoked: true });\n      },\n      update: async (options: ApiKeyUpdateOptions) => {\n        await this._interface.updateProjectApiKey(\n          crud.type === \"team\" ? { team_id: crud.team_id } : { user_id: crud.user_id },\n          crud.id,\n          await apiKeyUpdateOptionsToCrud(crud.type, options),\n          null,\n          \"server\");\n        if (crud.type === \"team\") {\n          await this._serverTeamApiKeysCache.refresh([crud.team_id]);\n        } else {\n          await this._serverUserApiKeysCache.refresh([crud.user_id]);\n        }\n      },\n    };\n  }\n\n  protected _serverUserFromCrud(crud: UsersCrud['Server']['Read']): ServerUser {\n    const app = this;\n\n    async function getConnectedAccount(id: ProviderType, options?: { scopes?: string[] }): Promise<OAuthConnection | null>;\n    async function getConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): Promise<OAuthConnection>;\n    async function getConnectedAccount(id: ProviderType, options?: { or?: 'redirect', scopes?: string[] }): Promise<OAuthConnection | null> {\n      const scopeString = options?.scopes?.join(\" \");\n      return Result.orThrow(await app._serverUserOAuthConnectionCache.getOrWait([crud.id, id, scopeString || \"\", options?.or === 'redirect'], \"write-only\"));\n    }\n\n    function useConnectedAccount(id: ProviderType, options?: { scopes?: string[] }): OAuthConnection | null;\n    function useConnectedAccount(id: ProviderType, options: { or: 'redirect', scopes?: string[] }): OAuthConnection;\n    function useConnectedAccount(id: ProviderType, options?: { or?: 'redirect', scopes?: string[] }): OAuthConnection | null {\n      const scopeString = options?.scopes?.join(\" \");\n      return useAsyncCache(app._serverUserOAuthConnectionCache, [crud.id, id, scopeString || \"\", options?.or === 'redirect'] as const, \"user.useConnectedAccount()\");\n    }\n\n    return {\n      ...super._createBaseUser(crud),\n      lastActiveAt: new Date(crud.last_active_at_millis),\n      serverMetadata: crud.server_metadata,\n      async setPrimaryEmail(email: string | null, options?: { verified?: boolean }) {\n        await app._updateServerUser(crud.id, { primaryEmail: email, primaryEmailVerified: options?.verified });\n      },\n      async grantPermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<void> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string' && permissionId) {\n          const scope = scopeOrPermissionId;\n          await app._interface.grantServerTeamUserPermission(scope.id, crud.id, permissionId);\n\n          for (const recursive of [true, false]) {\n            await app._serverTeamUserPermissionsCache.refresh([scope.id, crud.id, recursive]);\n          }\n        } else {\n          const pId = scopeOrPermissionId as string;\n          await app._interface.grantServerProjectPermission(crud.id, pId);\n\n          for (const recursive of [true, false]) {\n            await app._serverUserProjectPermissionsCache.refresh([crud.id, recursive]);\n          }\n        }\n      },\n      async revokePermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<void> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string' && permissionId) {\n          const scope = scopeOrPermissionId;\n          await app._interface.revokeServerTeamUserPermission(scope.id, crud.id, permissionId);\n\n          for (const recursive of [true, false]) {\n            await app._serverTeamUserPermissionsCache.refresh([scope.id, crud.id, recursive]);\n          }\n        } else {\n          const pId = scopeOrPermissionId as string;\n          await app._interface.revokeServerProjectPermission(crud.id, pId);\n\n          for (const recursive of [true, false]) {\n            await app._serverUserProjectPermissionsCache.refresh([crud.id, recursive]);\n          }\n        }\n      },\n      async delete() {\n        const res = await app._interface.deleteServerUser(crud.id);\n        await app._refreshUsers();\n        return res;\n      },\n      async createSession(options: { expiresInMillis?: number, isImpersonation?: boolean }) {\n        // TODO this should also refresh the access token when it expires (like InternalSession)\n        const tokens = await app._interface.createServerUserSession(crud.id, options.expiresInMillis ?? 1000 * 60 * 60 * 24 * 365, options.isImpersonation ?? false);\n        return {\n          async getTokens() {\n            return tokens;\n          },\n        };\n      },\n\n      async getActiveSessions() {\n        const sessions = await app._interface.listServerSessions(crud.id);\n        return sessions.map((session) => app._clientSessionFromCrud(session));\n      },\n\n      async revokeSession(sessionId: string) {\n        await app._interface.deleteServerSession(sessionId);\n      },\n      async setDisplayName(displayName: string) {\n        return await this.update({ displayName });\n      },\n      async setClientMetadata(metadata: Record<string, any>) {\n        return await this.update({ clientMetadata: metadata });\n      },\n      async setClientReadOnlyMetadata(metadata: Record<string, any>) {\n        return await this.update({ clientReadOnlyMetadata: metadata });\n      },\n      async setServerMetadata(metadata: Record<string, any>) {\n        return await this.update({ serverMetadata: metadata });\n      },\n      async setSelectedTeam(team: Team | null) {\n        return await this.update({ selectedTeamId: team?.id ?? null });\n      },\n      getConnectedAccount,\n      useConnectedAccount, // THIS_LINE_PLATFORM react-like\n      selectedTeam: crud.selected_team ? app._serverTeamFromCrud(crud.selected_team) : null,\n      async getTeam(teamId: string) {\n        const teams = await this.listTeams();\n        return teams.find((t) => t.id === teamId) ?? null;\n      },\n      useTeam(teamId: string) {\n        const teams = this.useTeams();\n        return useMemo(() => {\n          return teams.find((t) => t.id === teamId) ?? null;\n        }, [teams, teamId]);\n      },\n      async listTeams() {\n        const teams = Result.orThrow(await app._serverTeamsCache.getOrWait([crud.id], \"write-only\"));\n        return teams.map((t) => app._serverTeamFromCrud(t));\n      },\n      useTeams() {\n        const teams = useAsyncCache(app._serverTeamsCache, [crud.id], \"user.useTeams()\");\n        return useMemo(() => teams.map((t) => app._serverTeamFromCrud(t)), [teams]);\n      },\n      createTeam: async (data: Omit<ServerTeamCreateOptions, \"creatorUserId\">) => {\n        const team = await app._interface.createServerTeam(serverTeamCreateOptionsToCrud({\n          creatorUserId: crud.id,\n          ...data,\n        }));\n        await app._serverTeamsCache.refresh([undefined]);\n        await app._updateServerUser(crud.id, { selectedTeamId: team.id });\n        return app._serverTeamFromCrud(team);\n      },\n      leaveTeam: async (team: Team) => {\n        await app._interface.leaveServerTeam({ teamId: team.id, userId: crud.id });\n        // TODO: refresh cache\n      },\n      async listPermissions(scopeOrOptions?: Team | { recursive?: boolean }, options?: { recursive?: boolean }): Promise<AdminTeamPermission[]> {\n        if (scopeOrOptions && 'id' in scopeOrOptions) {\n          const scope = scopeOrOptions;\n          const recursive = options?.recursive ?? true;\n          const permissions = Result.orThrow(await app._serverTeamUserPermissionsCache.getOrWait([scope.id, crud.id, recursive], \"write-only\"));\n          return permissions.map((crud) => app._serverPermissionFromCrud(crud));\n        } else {\n          const opts = scopeOrOptions;\n          const recursive = opts?.recursive ?? true;\n          const permissions = Result.orThrow(await app._serverUserProjectPermissionsCache.getOrWait([crud.id, recursive], \"write-only\"));\n          return permissions.map((crud) => app._serverPermissionFromCrud(crud));\n        }\n      },\n      usePermissions(scopeOrOptions?: Team | { recursive?: boolean }, options?: { recursive?: boolean }): AdminTeamPermission[] {\n        if (scopeOrOptions && 'id' in scopeOrOptions) {\n          const scope = scopeOrOptions;\n          const recursive = options?.recursive ?? true;\n          const permissions = useAsyncCache(app._serverTeamUserPermissionsCache, [scope.id, crud.id, recursive] as const, \"user.usePermissions()\");\n          return useMemo(() => permissions.map((crud) => app._serverPermissionFromCrud(crud)), [permissions]);\n        } else {\n          const opts = scopeOrOptions;\n          const recursive = opts?.recursive ?? true;\n          const permissions = useAsyncCache(app._serverUserProjectPermissionsCache, [crud.id, recursive] as const, \"user.usePermissions()\");\n          return useMemo(() => permissions.map((crud) => app._serverPermissionFromCrud(crud)), [permissions]);\n        }\n      },\n      async getPermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<AdminTeamPermission | null> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          const permissions = await this.listPermissions(scope);\n          return permissions.find((p) => p.id === permissionId) ?? null;\n        } else {\n          const pid = scopeOrPermissionId;\n          const permissions = await this.listPermissions();\n          return permissions.find((p) => p.id === pid) ?? null;\n        }\n      },\n      usePermission(scopeOrPermissionId: Team | string, permissionId?: string): AdminTeamPermission | null {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          const permissions = this.usePermissions(scope);\n          return useMemo(() => permissions.find((p) => p.id === permissionId) ?? null, [permissions, permissionId]);\n        } else {\n          const pid = scopeOrPermissionId;\n          const permissions = this.usePermissions();\n          return useMemo(() => permissions.find((p) => p.id === pid) ?? null, [permissions, pid]);\n        }\n      },\n      async hasPermission(scopeOrPermissionId: Team | string, permissionId?: string): Promise<boolean> {\n        if (scopeOrPermissionId && typeof scopeOrPermissionId !== 'string') {\n          const scope = scopeOrPermissionId;\n          return (await this.getPermission(scope, permissionId as string)) !== null;\n        } else {\n          const pid = scopeOrPermissionId;\n          return (await this.getPermission(pid)) !== null;\n        }\n      },\n      async update(update: ServerUserUpdateOptions) {\n        await app._updateServerUser(crud.id, update);\n      },\n      async sendVerificationEmail() {\n        return await app._checkFeatureSupport(\"sendVerificationEmail() on ServerUser\", {});\n      },\n      async updatePassword(options: { oldPassword: string, newPassword: string}) {\n        const result = await app._interface.updatePassword(options);\n        await app._serverUserCache.refresh([crud.id]);\n        return result;\n      },\n      async setPassword(options: { password: string }) {\n        const result = await this.update(options);\n        await app._serverUserCache.refresh([crud.id]);\n        return result;\n      },\n      async getTeamProfile(team: Team) {\n        const result = Result.orThrow(await app._serverUserTeamProfileCache.getOrWait([team.id, crud.id], \"write-only\"));\n        return app._serverEditableTeamProfileFromCrud(result);\n      },\n      useTeamProfile(team: Team) {\n        const result = useAsyncCache(app._serverUserTeamProfileCache, [team.id, crud.id] as const, \"user.useTeamProfile()\");\n        return useMemo(() => app._serverEditableTeamProfileFromCrud(result), [result]);\n      },\n      async listContactChannels() {\n        const result = Result.orThrow(await app._serverContactChannelsCache.getOrWait([crud.id], \"write-only\"));\n        return result.map((data) => app._serverContactChannelFromCrud(crud.id, data));\n      },\n      useContactChannels() {\n        const result = useAsyncCache(app._serverContactChannelsCache, [crud.id] as const, \"user.useContactChannels()\");\n        return useMemo(() => result.map((data) => app._serverContactChannelFromCrud(crud.id, data)), [result]);\n      },\n      createContactChannel: async (data: ServerContactChannelCreateOptions) => {\n        const contactChannel = await app._interface.createServerContactChannel(serverContactChannelCreateOptionsToCrud(crud.id, data));\n        await Promise.all([\n          app._serverContactChannelsCache.refresh([crud.id]),\n          app._serverUserCache.refresh([crud.id])\n        ]);\n        return app._serverContactChannelFromCrud(crud.id, contactChannel);\n      },\n      useApiKeys() {\n        const result = useAsyncCache(app._serverUserApiKeysCache, [crud.id] as const, \"user.useApiKeys()\");\n        return result.map((apiKey) => app._serverApiKeyFromCrud(apiKey));\n      },\n      async listApiKeys() {\n        const result = Result.orThrow(await app._serverUserApiKeysCache.getOrWait([crud.id], \"write-only\"));\n        return result.map((apiKey) => app._serverApiKeyFromCrud(apiKey));\n      },\n      async createApiKey(options: ApiKeyCreationOptions<\"user\">) {\n        const result = await app._interface.createProjectApiKey(\n          await apiKeyCreationOptionsToCrud(\"user\", crud.id, options),\n          null,\n          \"server\",\n        );\n        await app._serverUserApiKeysCache.refresh([crud.id]);\n        return app._serverApiKeyFromCrud(result);\n      },\n    };\n  }\n\n  protected _serverTeamUserFromCrud(crud: TeamMemberProfilesCrud[\"Server\"][\"Read\"]): ServerTeamUser {\n    return {\n      ...this._serverUserFromCrud(crud.user),\n      teamProfile: {\n        displayName: crud.display_name,\n        profileImageUrl: crud.profile_image_url,\n      },\n    };\n  }\n\n  protected _serverTeamInvitationFromCrud(crud: TeamInvitationCrud['Server']['Read']): TeamInvitation {\n    return {\n      id: crud.id,\n      recipientEmail: crud.recipient_email,\n      expiresAt: new Date(crud.expires_at_millis),\n      revoke: async () => {\n        await this._interface.revokeServerTeamInvitation(crud.id, crud.team_id);\n      },\n    };\n  }\n\n  protected override _currentUserFromCrud(crud: UsersCrud['Server']['Read'], session: InternalSession): ProjectCurrentServerUser<ProjectId> {\n    const app = this;\n    const currentUser = {\n      ...this._serverUserFromCrud(crud),\n      ...this._createAuth(session),\n      ...this._isInternalProject() ? this._createInternalUserExtra(session) : {},\n    } satisfies ServerUser;\n\n    Object.freeze(currentUser);\n    return currentUser as ProjectCurrentServerUser<ProjectId>;\n  }\n\n  protected _serverTeamFromCrud(crud: TeamsCrud['Server']['Read']): ServerTeam {\n    const app = this;\n    return {\n      id: crud.id,\n      displayName: crud.display_name,\n      profileImageUrl: crud.profile_image_url,\n      createdAt: new Date(crud.created_at_millis),\n      clientMetadata: crud.client_metadata,\n      clientReadOnlyMetadata: crud.client_read_only_metadata,\n      serverMetadata: crud.server_metadata,\n      async update(update: Partial<ServerTeamUpdateOptions>) {\n        await app._interface.updateServerTeam(crud.id, serverTeamUpdateOptionsToCrud(update));\n        await app._serverTeamsCache.refresh([undefined]);\n      },\n      async delete() {\n        await app._interface.deleteServerTeam(crud.id);\n        await app._serverTeamsCache.refresh([undefined]);\n      },\n      async listUsers() {\n        const result = Result.orThrow(await app._serverTeamMemberProfilesCache.getOrWait([crud.id], \"write-only\"));\n        return result.map(u => app._serverTeamUserFromCrud(u));\n      },\n      useUsers() {\n        const result = useAsyncCache(app._serverTeamMemberProfilesCache, [crud.id] as const, \"team.useUsers()\");\n        return useMemo(() => result.map(u => app._serverTeamUserFromCrud(u)), [result]);\n      },\n      async addUser(userId) {\n        await app._interface.addServerUserToTeam({\n          teamId: crud.id,\n          userId,\n        });\n        await app._serverTeamMemberProfilesCache.refresh([crud.id]);\n      },\n      async removeUser(userId) {\n        await app._interface.removeServerUserFromTeam({\n          teamId: crud.id,\n          userId,\n        });\n        await app._serverTeamMemberProfilesCache.refresh([crud.id]);\n      },\n      async inviteUser(options: { email: string, callbackUrl?: string }) {\n        await app._interface.sendServerTeamInvitation({\n          teamId: crud.id,\n          email: options.email,\n          callbackUrl: options.callbackUrl ?? constructRedirectUrl(app.urls.teamInvitation, \"callbackUrl\"),\n        });\n        await app._serverTeamInvitationsCache.refresh([crud.id]);\n      },\n      async listInvitations() {\n        const result = Result.orThrow(await app._serverTeamInvitationsCache.getOrWait([crud.id], \"write-only\"));\n        return result.map((crud) => app._serverTeamInvitationFromCrud(crud));\n      },\n      useInvitations() {\n        const result = useAsyncCache(app._serverTeamInvitationsCache, [crud.id] as const, \"team.useInvitations()\");\n        return useMemo(() => result.map((crud) => app._serverTeamInvitationFromCrud(crud)), [result]);\n      },\n      useApiKeys() {\n        const result = useAsyncCache(app._serverTeamApiKeysCache, [crud.id] as const, \"team.useApiKeys()\");\n        return result.map((apiKey) => app._serverApiKeyFromCrud(apiKey));\n      },\n      async listApiKeys() {\n        const result = Result.orThrow(await app._serverTeamApiKeysCache.getOrWait([crud.id], \"write-only\"));\n        return result.map((apiKey) => app._serverApiKeyFromCrud(apiKey));\n      },\n      async createApiKey(options: ApiKeyCreationOptions<\"team\">) {\n        const result = await app._interface.createProjectApiKey(\n          await apiKeyCreationOptionsToCrud(\"team\", crud.id, options),\n          null,\n          \"server\",\n        );\n        await app._serverTeamApiKeysCache.refresh([crud.id]);\n        return app._serverApiKeyFromCrud(result);\n      },\n    };\n  }\n\n  protected async _getUserApiKey(options: { apiKey: string }): Promise<ApiKey<\"user\"> | null> {\n    const crud = Result.orThrow(await this._serverCheckApiKeyCache.getOrWait([\"user\", options.apiKey], \"write-only\")) as UserApiKeysCrud['Server']['Read'] | null;\n    return crud ? this._serverApiKeyFromCrud(crud) : null;\n  }\n\n  protected async _getTeamApiKey(options: { apiKey: string }): Promise<ApiKey<\"team\"> | null> {\n    const crud = Result.orThrow(await this._serverCheckApiKeyCache.getOrWait([\"team\", options.apiKey], \"write-only\")) as TeamApiKeysCrud['Server']['Read'] | null;\n    return crud ? this._serverApiKeyFromCrud(crud) : null;\n  }\n  protected _useUserApiKey(options: { apiKey: string }): ApiKey<\"user\"> | null {\n    const crud = useAsyncCache(this._serverCheckApiKeyCache, [\"user\", options.apiKey] as const, \"useUserApiKey()\") as UserApiKeysCrud['Server']['Read'] | null;\n    return useMemo(() => crud ? this._serverApiKeyFromCrud(crud) : null, [crud]);\n  }\n  protected _useTeamApiKey(options: { apiKey: string }): ApiKey<\"team\"> | null {\n    const crud = useAsyncCache(this._serverCheckApiKeyCache, [\"team\", options.apiKey] as const, \"useTeamApiKey()\") as TeamApiKeysCrud['Server']['Read'] | null;\n    return useMemo(() => crud ? this._serverApiKeyFromCrud(crud) : null, [crud]);\n  }\n  protected async _getUserByApiKey(apiKey: string): Promise<ServerUser | null> {\n    const apiKeyObject = await this._getUserApiKey({ apiKey });\n    if (apiKeyObject === null) {\n      return null;\n    }\n    return await this.getServerUserById(apiKeyObject.userId);\n  }\n  protected _useUserByApiKey(apiKey: string): ServerUser | null {\n    const apiKeyObject = this._useUserApiKey({ apiKey });\n    if (apiKeyObject === null) {\n      return null;\n    }\n    return this.useUserById(apiKeyObject.userId);\n  }\n\n  protected async _getTeamByApiKey(apiKey: string): Promise<ServerTeam | null> {\n    const apiKeyObject = await this._getTeamApiKey({ apiKey });\n    if (apiKeyObject === null) {\n      return null;\n    }\n    return await this.getTeam(apiKeyObject.teamId);\n  }\n  protected _useTeamByApiKey(apiKey: string): ServerTeam | null {\n    const apiKeyObject = this._useTeamApiKey({ apiKey });\n    if (apiKeyObject === null) {\n      return null;\n    }\n    return this.useTeam(apiKeyObject.teamId);\n  }\n\n  async createUser(options: ServerUserCreateOptions): Promise<ServerUser> {\n    const crud = await this._interface.createServerUser(serverUserCreateOptionsToCrud(options));\n    await this._refreshUsers();\n    return this._serverUserFromCrud(crud);\n  }\n\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): Promise<ProjectCurrentServerUser<ProjectId>>;\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): Promise<ProjectCurrentServerUser<ProjectId>>;\n  async getUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): Promise<ProjectCurrentServerUser<ProjectId>>;\n  async getUser(options?: GetUserOptions<HasTokenStore>): Promise<ProjectCurrentServerUser<ProjectId> | null>;\n  async getUser(id: string): Promise<ServerUser | null>;\n  async getUser(options: { apiKey: string }): Promise<ServerUser | null>;\n  async getUser(options?: string | GetUserOptions<HasTokenStore> | { apiKey: string }): Promise<ProjectCurrentServerUser<ProjectId> | ServerUser | null> {\n    if (typeof options === \"string\") {\n      return await this.getServerUserById(options);\n    } else if (typeof options === \"object\" && \"apiKey\" in options) {\n      return await this._getUserByApiKey(options.apiKey);\n    } else {\n      // TODO this code is duplicated from the client app; fix that\n      this._ensurePersistentTokenStore(options?.tokenStore);\n      const session = await this._getSession(options?.tokenStore);\n      let crud = Result.orThrow(await this._currentServerUserCache.getOrWait([session], \"write-only\"));\n      if (crud?.is_anonymous && options?.or !== \"anonymous\" && options?.or !== \"anonymous-if-exists\") {\n        crud = null;\n      }\n\n      if (crud === null) {\n        switch (options?.or) {\n          case 'redirect': {\n            await this.redirectToSignIn({ replace: true });\n            break;\n          }\n          case 'throw': {\n            throw new Error(\"User is not signed in but getUser was called with { or: 'throw' }\");\n          }\n          case 'anonymous': {\n            const tokens = await this._signUpAnonymously();\n            return await this.getUser({ tokenStore: tokens, or: \"anonymous-if-exists\" }) ?? throwErr(\"Something went wrong while signing up anonymously\");\n          }\n          case undefined:\n          case \"anonymous-if-exists\":\n          case \"return-null\": {\n            return null;\n          }\n        }\n      }\n\n      return crud && this._currentUserFromCrud(crud, session);\n    }\n  }\n\n  async getServerUser(): Promise<ProjectCurrentServerUser<ProjectId> | null> {\n    console.warn(\"stackServerApp.getServerUser is deprecated; use stackServerApp.getUser instead\");\n    return await this.getUser();\n  }\n\n  async getServerUserById(userId: string): Promise<ServerUser | null> {\n    const crud = Result.orThrow(await this._serverUserCache.getOrWait([userId], \"write-only\"));\n    return crud && this._serverUserFromCrud(crud);\n  }\n\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'redirect' }): ProjectCurrentServerUser<ProjectId>;\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'throw' }): ProjectCurrentServerUser<ProjectId>;\n  useUser(options: GetUserOptions<HasTokenStore> & { or: 'anonymous' }): ProjectCurrentServerUser<ProjectId>;\n  useUser(options?: GetUserOptions<HasTokenStore>): ProjectCurrentServerUser<ProjectId> | null;\n  useUser(id: string): ServerUser | null;\n  useUser(options: { apiKey: string }): ServerUser | null;\n  useUser(options?: GetUserOptions<HasTokenStore> | string | { apiKey: string }): ProjectCurrentServerUser<ProjectId> | ServerUser | null {\n    if (typeof options === \"string\") {\n      return this.useUserById(options);\n    } else if (typeof options === \"object\" && \"apiKey\" in options) {\n      return this._useUserByApiKey(options.apiKey);\n    } else {\n      // TODO this code is duplicated from the client app; fix that\n      this._ensurePersistentTokenStore(options?.tokenStore);\n\n      const session = this._useSession(options?.tokenStore);\n      let crud = useAsyncCache(this._currentServerUserCache, [session] as const, \"useUser()\");\n      if (crud?.is_anonymous && options?.or !== \"anonymous\" && options?.or !== \"anonymous-if-exists\") {\n        crud = null;\n      }\n\n      if (crud === null) {\n        switch (options?.or) {\n          case 'redirect': {\n            runAsynchronously(this.redirectToSignIn({ replace: true }));\n            suspend();\n            throw new StackAssertionError(\"suspend should never return\");\n          }\n          case 'throw': {\n            throw new Error(\"User is not signed in but useUser was called with { or: 'throw' }\");\n          }\n          case 'anonymous': {\n            // TODO we should think about the behavior when calling useUser (or getUser) in anonymous with a custom token store. signUpAnonymously always sets the current token store on app level, instead of the one passed to this function\n            // TODO we shouldn't reload & suspend here, instead we should use a promise that resolves to the new anonymous user\n            runAsynchronously(async () => {\n              await this._signUpAnonymously();\n              if (typeof window !== \"undefined\") {\n                window.location.reload();\n              }\n            });\n            suspend();\n            throw new StackAssertionError(\"suspend should never return\");\n          }\n          case undefined:\n          case \"anonymous-if-exists\":\n          case \"return-null\": {\n            // do nothing\n          }\n        }\n      }\n\n      return useMemo(() => {\n        return crud && this._currentUserFromCrud(crud, session);\n      }, [crud, session, options?.or]);\n    }\n  }\n  useUserById(userId: string): ServerUser | null {\n    const crud = useAsyncCache(this._serverUserCache, [userId], \"useUserById()\");\n    return useMemo(() => {\n      return crud && this._serverUserFromCrud(crud);\n    }, [crud]);\n  }\n\n  async listUsers(options?: ServerListUsersOptions): Promise<ServerUser[] & { nextCursor: string | null }> {\n    const crud = Result.orThrow(await this._serverUsersCache.getOrWait([options?.cursor, options?.limit, options?.orderBy, options?.desc, options?.query], \"write-only\"));\n    const result: any = crud.items.map((j) => this._serverUserFromCrud(j));\n    result.nextCursor = crud.pagination?.next_cursor ?? null;\n    return result as any;\n  }\n\n  useUsers(options?: ServerListUsersOptions): ServerUser[] & { nextCursor: string | null } {\n    const crud = useAsyncCache(this._serverUsersCache, [options?.cursor, options?.limit, options?.orderBy, options?.desc, options?.query] as const, \"useServerUsers()\");\n    const result: any = crud.items.map((j) => this._serverUserFromCrud(j));\n    result.nextCursor = crud.pagination?.next_cursor ?? null;\n    return result as any;\n  }\n\n  _serverPermissionFromCrud(crud: TeamPermissionsCrud['Server']['Read'] | ProjectPermissionsCrud['Server']['Read']): AdminTeamPermission {\n    return {\n      id: crud.id,\n    };\n  }\n\n  _serverTeamPermissionDefinitionFromCrud(crud: TeamPermissionDefinitionsCrud['Admin']['Read']): AdminTeamPermissionDefinition {\n    return {\n      id: crud.id,\n      description: crud.description,\n      containedPermissionIds: crud.contained_permission_ids,\n    };\n  }\n\n  _serverProjectPermissionDefinitionFromCrud(crud: ProjectPermissionDefinitionsCrud['Admin']['Read']): AdminProjectPermissionDefinition {\n    return {\n      id: crud.id,\n      description: crud.description,\n      containedPermissionIds: crud.contained_permission_ids,\n    };\n  }\n\n  async listTeams(): Promise<ServerTeam[]> {\n    const teams = Result.orThrow(await this._serverTeamsCache.getOrWait([undefined], \"write-only\"));\n    return teams.map((t) => this._serverTeamFromCrud(t));\n  }\n\n  async createTeam(data: ServerTeamCreateOptions): Promise<ServerTeam> {\n    const team = await this._interface.createServerTeam(serverTeamCreateOptionsToCrud(data));\n    await this._serverTeamsCache.refresh([undefined]);\n    return this._serverTeamFromCrud(team);\n  }\n\n  useTeams(): ServerTeam[] {\n    const teams = useAsyncCache(this._serverTeamsCache, [undefined], \"useServerTeams()\");\n    return useMemo(() => {\n      return teams.map((t) => this._serverTeamFromCrud(t));\n    }, [teams]);\n  }\n\n  async getTeam(options: { apiKey: string }): Promise<ServerTeam | null>;\n  async getTeam(teamId: string): Promise<ServerTeam | null>;\n  async getTeam(options?: { apiKey: string } | string): Promise<ServerTeam | null> {\n    if (typeof options === \"object\" && \"apiKey\" in options) {\n      return await this._getTeamByApiKey(options.apiKey);\n    } else {\n      const teamId = options;\n      const teams = await this.listTeams();\n      return teams.find((t) => t.id === teamId) ?? null;\n    }\n  }\n\n  useTeam(options: { apiKey: string }): ServerTeam | null;\n  useTeam(teamId: string): ServerTeam | null;\n  useTeam(options?: { apiKey: string } | string): ServerTeam | null {\n    if (typeof options === \"object\" && \"apiKey\" in options) {\n      return this._useTeamByApiKey(options.apiKey);\n    } else {\n      const teamId = options;\n      const teams = this.useTeams();\n      return useMemo(() => {\n        return teams.find((t) => t.id === teamId) ?? null;\n      }, [teams, teamId]);\n    }\n  }\n\n  protected override async _refreshSession(session: InternalSession) {\n    await Promise.all([\n      super._refreshUser(session),\n      this._currentServerUserCache.refresh([session]),\n    ]);\n  }\n\n  protected override async _refreshUsers() {\n    await Promise.all([\n      super._refreshUsers(),\n      this._serverUserCache.refreshWhere(() => true),\n      this._serverUsersCache.refreshWhere(() => true),\n      this._serverContactChannelsCache.refreshWhere(() => true),\n    ]);\n  }\n}\n"], "mappings": ";AAIA,SAAS,aAAa,4BAA4B;AAUlD,SAAS,qBAAqB,gBAAgB;AAE9C,SAAS,yBAAyB;AAClC,SAAS,eAAe;AACxB,SAAS,cAAc;AACvB,SAAS,eAAe;AAExB,SAAS,4BAA4B;AACrC,SAA6D,6BAA6B,iCAAiC;AAG3H,SAAqG,yCAAyC,+CAA+C;AAE7L,SAAgK,+BAA+B,qCAAqC;AACpO,SAAiG,+BAA+B,qCAAqC;AAErK,SAAS,qCAAqC;AAC9C,SAAS,eAAe,aAAa,sBAAsB,YAAY,qBAAqB,gCAAgC,iCAAiC;AAE7J,SAAS,qBAAqB;AAEvB,IAAM,gCAAN,cAAqG,8BAC5G;AAAA,EAwKE,YAAY,SAQV;AACA,UAAM,eAAe,UAAU;AAAA,MAC7B,WAAW,QAAQ;AAAA,MACnB,YAAY,QAAQ;AAAA,MACpB,MAAM,QAAQ;AAAA,MACd,qBAAqB,QAAQ;AAAA,IAC/B,IAAI;AAAA,MACF,WAAW,IAAI,qBAAqB;AAAA,QAClC,YAAY,MAAM,WAAW,QAAQ,OAAO;AAAA,QAC5C,WAAW,QAAQ,aAAa,oBAAoB;AAAA,QACpD,qBAAqB,QAAQ,uBAAuB,CAAC;AAAA,QACrD;AAAA,QACA,sBAAsB,QAAQ,wBAAwB,+BAA+B;AAAA,QACrF,iBAAiB,QAAQ,mBAAmB,0BAA0B;AAAA,MACxE,CAAC;AAAA,MACD,SAAS,QAAQ;AAAA,MACjB,qBAAqB,QAAQ;AAAA,MAC7B,WAAW,QAAQ;AAAA,MACnB,sBAAsB,QAAQ;AAAA,MAC9B,YAAY,QAAQ;AAAA,MACpB,MAAM,QAAQ;AAAA,MACd,qBAAqB,QAAQ;AAAA,MAC7B,gBAAgB,QAAQ;AAAA,IAC1B,CAAC;AAnMH;AAAA,SAAiB,0BAA0B,qBAAqB,OAAO,YAAY;AACjF,UAAI,QAAQ,mBAAmB,GAAG;AAEhC,eAAO;AAAA,MACT;AACA,aAAO,MAAM,KAAK,WAAW,qBAAqB,OAAO;AAAA,IAC3D,CAAC;AACD,SAAiB,oBAAoB,YAML,OAAO,CAAC,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM;AAC/E,aAAO,MAAM,KAAK,WAAW,gBAAgB,EAAE,QAAQ,OAAO,SAAS,MAAM,MAAM,CAAC;AAAA,IACtF,CAAC;AACD,SAAiB,mBAAmB,YAA0D,OAAO,CAAC,MAAM,MAAM;AAChH,YAAM,OAAO,MAAM,KAAK,WAAW,kBAAkB,MAAM;AAC3D,aAAO,OAAO,GAAG,MAAM,IAAI;AAAA,IAC7B,CAAC;AACD,SAAiB,oBAAoB,YAAiE,OAAO,CAAC,MAAM,MAAM;AACxH,aAAO,MAAM,KAAK,WAAW,gBAAgB,EAAE,OAAO,CAAC;AAAA,IACzD,CAAC;AACD,SAAiB,kCAAkC,YAGjD,OAAO,CAAC,QAAQ,QAAQ,SAAS,MAAM;AACvC,aAAO,MAAM,KAAK,WAAW,0BAA0B,EAAE,QAAQ,QAAQ,UAAU,GAAG,IAAI;AAAA,IAC5F,CAAC;AACD,SAAiB,qCAAqC,YAGpD,OAAO,CAAC,QAAQ,SAAS,MAAM;AAC/B,aAAO,MAAM,KAAK,WAAW,6BAA6B,EAAE,QAAQ,UAAU,GAAG,IAAI;AAAA,IACvF,CAAC;AACD,SAAiB,8CAA8C;AAAA,MAC7D,OAAO,CAAC,QAAQ,YAAY,KAAK,MAAM;AACrC,YAAI;AACF,gBAAM,SAAS,MAAM,KAAK,WAAW,gCAAgC,QAAQ,YAAY,SAAS,EAAE;AACpG,iBAAO,EAAE,aAAa,OAAO,aAAa;AAAA,QAC5C,SAAS,KAAK;AACZ,cAAI,EAAE,YAAY,wCAAwC,WAAW,GAAG,KAAK,YAAY,kCAAkC,WAAW,GAAG,IAAI;AAC3I,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAiB,kCAAkC;AAAA,MACjD,OAAO,CAAC,QAAQ,YAAY,OAAO,QAAQ,MAAM;AAC/C,eAAO,MAAM,KAAK,+BAA+B;AAAA,UAC/C,SAAS,YAAY,OAAO,QAAQ,MAAM,KAAK,iBAAiB,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;AAAA,UACjG,qBAAqB,YAAY,OAAO,QAAQ,MAAM,KAAK,4CAA4C,UAAU,CAAC,QAAQ,YAAY,SAAS,EAAE,GAAY,YAAY,CAAC;AAAA,UAC1K,eAAe,MAAM,cAAc,KAAK,6CAA6C,CAAC,QAAQ,YAAY,SAAS,EAAE,GAAY,4BAA4B;AAAA,UAC7J;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AACA,SAAiB,iCAAiC;AAAA,MAChD,OAAO,CAAC,MAAM,MAAM;AAClB,eAAO,MAAM,KAAK,WAAW,6BAA6B,EAAE,OAAO,CAAC;AAAA,MACtE;AAAA,IACF;AACA,SAAiB,8BAA8B;AAAA,MAC7C,OAAO,CAAC,MAAM,MAAM;AAClB,eAAO,MAAM,KAAK,WAAW,0BAA0B,EAAE,OAAO,CAAC;AAAA,MACnE;AAAA,IACF;AACA,SAAiB,8BAA8B;AAAA,MAC7C,OAAO,CAAC,QAAQ,MAAM,MAAM;AAC1B,eAAO,MAAM,KAAK,WAAW,2BAA2B,EAAE,QAAQ,OAAO,CAAC;AAAA,MAC5E;AAAA,IACF;AACA,SAAiB,8BAA8B;AAAA,MAC7C,OAAO,CAAC,MAAM,MAAM;AAClB,eAAO,MAAM,KAAK,WAAW,0BAA0B,MAAM;AAAA,MAC/D;AAAA,IACF;AAEA,SAAiB,0BAA0B;AAAA,MACzC,OAAO,CAAC,MAAM,MAAM;AAClB,cAAM,SAAS,MAAM,KAAK,WAAW,mBAAmB;AAAA,UACtD,SAAS;AAAA,QACX,GAAG,MAAM,QAAQ;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAiB,0BAA0B;AAAA,MACzC,OAAO,CAAC,MAAM,MAAM;AAClB,cAAM,SAAS,MAAM,KAAK,WAAW,mBAAmB;AAAA,UACtD,SAAS;AAAA,QACX,GAAG,MAAM,QAAQ;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,SAAiB,0BAA0B,YAAqH,OAAO,CAAC,MAAM,MAAM,MAAM;AACxL,YAAM,SAAS,MAAM,KAAK,WAAW;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EAwFD;AAAA,EAtFA,MAAc,kBAAkB,QAAgB,QAAuE;AACrH,UAAM,SAAS,MAAM,KAAK,WAAW,iBAAiB,QAAQ,8BAA8B,MAAM,CAAC;AACnG,UAAM,KAAK,cAAc;AACzB,WAAO;AAAA,EACT;AAAA,EAEU,mCAAmC,MAA2E;AACtH,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,aAAa,KAAK;AAAA,MAClB,iBAAiB,KAAK;AAAA,MACtB,MAAM,OAAO,QAA4D;AACvE,cAAM,IAAI,WAAW,8BAA8B;AAAA,UACjD,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,UACb,SAAS;AAAA,YACP,cAAc,OAAO;AAAA,YACrB,mBAAmB,OAAO;AAAA,UAC5B;AAAA,QACF,CAAC;AACD,cAAM,IAAI,4BAA4B,QAAQ,CAAC,KAAK,SAAS,KAAK,OAAO,CAAC;AAAA,MAC5E;AAAA,IACF;AAAA,EACF;AAAA,EAEU,8BAA8B,QAAgB,MAAmE;AACzH,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,aAAa,KAAK;AAAA,MAClB,MAAM,sBAAsB,SAAoC;AAC9D,cAAM,IAAI,WAAW,0CAA0C,QAAQ,KAAK,IAAI,SAAS,eAAe,qBAAqB,IAAI,KAAK,mBAAmB,aAAa,CAAC;AAAA,MACzK;AAAA,MACA,MAAM,OAAO,MAAyC;AACpD,cAAM,IAAI,WAAW,2BAA2B,QAAQ,KAAK,IAAI,wCAAwC,IAAI,CAAC;AAC9G,cAAM,QAAQ,IAAI;AAAA,UAChB,IAAI,4BAA4B,QAAQ,CAAC,MAAM,CAAC;AAAA,UAChD,IAAI,iBAAiB,QAAQ,CAAC,MAAM,CAAC;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,2BAA2B,QAAQ,KAAK,EAAE;AAC/D,cAAM,QAAQ,IAAI;AAAA,UAChB,IAAI,4BAA4B,QAAQ,CAAC,MAAM,CAAC;AAAA,UAChD,IAAI,iBAAiB,QAAQ,CAAC,MAAM,CAAC;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EAyCU,sBAAsB,MAA2N;AACzP,WAAO;AAAA,MACL,GAAG,KAAK,oBAAoB,IAAI;AAAA,MAChC,MAAM,SAAS;AACb,cAAM,KAAK,OAAO,EAAE,SAAS,KAAK,CAAC;AAAA,MACrC;AAAA,MACA,QAAQ,OAAO,YAAiC;AAC9C,cAAM,KAAK,WAAW;AAAA,UACpB,KAAK,SAAS,SAAS,EAAE,SAAS,KAAK,QAAQ,IAAI,EAAE,SAAS,KAAK,QAAQ;AAAA,UAC3E,KAAK;AAAA,UACL,MAAM,0BAA0B,KAAK,MAAM,OAAO;AAAA,UAClD;AAAA,UACA;AAAA,QAAQ;AACV,YAAI,KAAK,SAAS,QAAQ;AACxB,gBAAM,KAAK,wBAAwB,QAAQ,CAAC,KAAK,OAAO,CAAC;AAAA,QAC3D,OAAO;AACL,gBAAM,KAAK,wBAAwB,QAAQ,CAAC,KAAK,OAAO,CAAC;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEU,oBAAoB,MAA+C;AAC3E,UAAM,MAAM;AAIZ,mBAAe,oBAAoB,IAAkB,SAAmF;AACtI,YAAM,cAAc,SAAS,QAAQ,KAAK,GAAG;AAC7C,aAAO,OAAO,QAAQ,MAAM,IAAI,gCAAgC,UAAU,CAAC,KAAK,IAAI,IAAI,eAAe,IAAI,SAAS,OAAO,UAAU,GAAG,YAAY,CAAC;AAAA,IACvJ;AAIA,aAAS,oBAAoB,IAAkB,SAA0E;AACvH,YAAM,cAAc,SAAS,QAAQ,KAAK,GAAG;AAC7C,aAAO,cAAc,IAAI,iCAAiC,CAAC,KAAK,IAAI,IAAI,eAAe,IAAI,SAAS,OAAO,UAAU,GAAY,4BAA4B;AAAA,IAC/J;AAEA,WAAO;AAAA,MACL,GAAG,MAAM,gBAAgB,IAAI;AAAA,MAC7B,cAAc,IAAI,KAAK,KAAK,qBAAqB;AAAA,MACjD,gBAAgB,KAAK;AAAA,MACrB,MAAM,gBAAgB,OAAsB,SAAkC;AAC5E,cAAM,IAAI,kBAAkB,KAAK,IAAI,EAAE,cAAc,OAAO,sBAAsB,SAAS,SAAS,CAAC;AAAA,MACvG;AAAA,MACA,MAAM,gBAAgB,qBAAoC,cAAsC;AAC9F,YAAI,uBAAuB,OAAO,wBAAwB,YAAY,cAAc;AAClF,gBAAM,QAAQ;AACd,gBAAM,IAAI,WAAW,8BAA8B,MAAM,IAAI,KAAK,IAAI,YAAY;AAElF,qBAAW,aAAa,CAAC,MAAM,KAAK,GAAG;AACrC,kBAAM,IAAI,gCAAgC,QAAQ,CAAC,MAAM,IAAI,KAAK,IAAI,SAAS,CAAC;AAAA,UAClF;AAAA,QACF,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,IAAI,WAAW,6BAA6B,KAAK,IAAI,GAAG;AAE9D,qBAAW,aAAa,CAAC,MAAM,KAAK,GAAG;AACrC,kBAAM,IAAI,mCAAmC,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC;AAAA,UAC3E;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,iBAAiB,qBAAoC,cAAsC;AAC/F,YAAI,uBAAuB,OAAO,wBAAwB,YAAY,cAAc;AAClF,gBAAM,QAAQ;AACd,gBAAM,IAAI,WAAW,+BAA+B,MAAM,IAAI,KAAK,IAAI,YAAY;AAEnF,qBAAW,aAAa,CAAC,MAAM,KAAK,GAAG;AACrC,kBAAM,IAAI,gCAAgC,QAAQ,CAAC,MAAM,IAAI,KAAK,IAAI,SAAS,CAAC;AAAA,UAClF;AAAA,QACF,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,IAAI,WAAW,8BAA8B,KAAK,IAAI,GAAG;AAE/D,qBAAW,aAAa,CAAC,MAAM,KAAK,GAAG;AACrC,kBAAM,IAAI,mCAAmC,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC;AAAA,UAC3E;AAAA,QACF;AAAA,MACF;AAAA,MACA,MAAM,SAAS;AACb,cAAM,MAAM,MAAM,IAAI,WAAW,iBAAiB,KAAK,EAAE;AACzD,cAAM,IAAI,cAAc;AACxB,eAAO;AAAA,MACT;AAAA,MACA,MAAM,cAAc,SAAkE;AAEpF,cAAM,SAAS,MAAM,IAAI,WAAW,wBAAwB,KAAK,IAAI,QAAQ,mBAAmB,MAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,mBAAmB,KAAK;AAC3J,eAAO;AAAA,UACL,MAAM,YAAY;AAChB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MAEA,MAAM,oBAAoB;AACxB,cAAM,WAAW,MAAM,IAAI,WAAW,mBAAmB,KAAK,EAAE;AAChE,eAAO,SAAS,IAAI,CAAC,YAAY,IAAI,uBAAuB,OAAO,CAAC;AAAA,MACtE;AAAA,MAEA,MAAM,cAAc,WAAmB;AACrC,cAAM,IAAI,WAAW,oBAAoB,SAAS;AAAA,MACpD;AAAA,MACA,MAAM,eAAe,aAAqB;AACxC,eAAO,MAAM,KAAK,OAAO,EAAE,YAAY,CAAC;AAAA,MAC1C;AAAA,MACA,MAAM,kBAAkB,UAA+B;AACrD,eAAO,MAAM,KAAK,OAAO,EAAE,gBAAgB,SAAS,CAAC;AAAA,MACvD;AAAA,MACA,MAAM,0BAA0B,UAA+B;AAC7D,eAAO,MAAM,KAAK,OAAO,EAAE,wBAAwB,SAAS,CAAC;AAAA,MAC/D;AAAA,MACA,MAAM,kBAAkB,UAA+B;AACrD,eAAO,MAAM,KAAK,OAAO,EAAE,gBAAgB,SAAS,CAAC;AAAA,MACvD;AAAA,MACA,MAAM,gBAAgB,MAAmB;AACvC,eAAO,MAAM,KAAK,OAAO,EAAE,gBAAgB,MAAM,MAAM,KAAK,CAAC;AAAA,MAC/D;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MACA,cAAc,KAAK,gBAAgB,IAAI,oBAAoB,KAAK,aAAa,IAAI;AAAA,MACjF,MAAM,QAAQ,QAAgB;AAC5B,cAAM,QAAQ,MAAM,KAAK,UAAU;AACnC,eAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,MAC/C;AAAA,MACA,QAAQ,QAAgB;AACtB,cAAM,QAAQ,KAAK,SAAS;AAC5B,eAAO,QAAQ,MAAM;AACnB,iBAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,QAC/C,GAAG,CAAC,OAAO,MAAM,CAAC;AAAA,MACpB;AAAA,MACA,MAAM,YAAY;AAChB,cAAM,QAAQ,OAAO,QAAQ,MAAM,IAAI,kBAAkB,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AAC3F,eAAO,MAAM,IAAI,CAAC,MAAM,IAAI,oBAAoB,CAAC,CAAC;AAAA,MACpD;AAAA,MACA,WAAW;AACT,cAAM,QAAQ,cAAc,IAAI,mBAAmB,CAAC,KAAK,EAAE,GAAG,iBAAiB;AAC/E,eAAO,QAAQ,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,oBAAoB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;AAAA,MAC5E;AAAA,MACA,YAAY,OAAO,SAAyD;AAC1E,cAAM,OAAO,MAAM,IAAI,WAAW,iBAAiB,8BAA8B;AAAA,UAC/E,eAAe,KAAK;AAAA,UACpB,GAAG;AAAA,QACL,CAAC,CAAC;AACF,cAAM,IAAI,kBAAkB,QAAQ,CAAC,MAAS,CAAC;AAC/C,cAAM,IAAI,kBAAkB,KAAK,IAAI,EAAE,gBAAgB,KAAK,GAAG,CAAC;AAChE,eAAO,IAAI,oBAAoB,IAAI;AAAA,MACrC;AAAA,MACA,WAAW,OAAO,SAAe;AAC/B,cAAM,IAAI,WAAW,gBAAgB,EAAE,QAAQ,KAAK,IAAI,QAAQ,KAAK,GAAG,CAAC;AAAA,MAE3E;AAAA,MACA,MAAM,gBAAgB,gBAAiD,SAAmE;AACxI,YAAI,kBAAkB,QAAQ,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,YAAY,SAAS,aAAa;AACxC,gBAAM,cAAc,OAAO,QAAQ,MAAM,IAAI,gCAAgC,UAAU,CAAC,MAAM,IAAI,KAAK,IAAI,SAAS,GAAG,YAAY,CAAC;AACpI,iBAAO,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC;AAAA,QACtE,OAAO;AACL,gBAAM,OAAO;AACb,gBAAM,YAAY,MAAM,aAAa;AACrC,gBAAM,cAAc,OAAO,QAAQ,MAAM,IAAI,mCAAmC,UAAU,CAAC,KAAK,IAAI,SAAS,GAAG,YAAY,CAAC;AAC7H,iBAAO,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC;AAAA,QACtE;AAAA,MACF;AAAA,MACA,eAAe,gBAAiD,SAA0D;AACxH,YAAI,kBAAkB,QAAQ,gBAAgB;AAC5C,gBAAM,QAAQ;AACd,gBAAM,YAAY,SAAS,aAAa;AACxC,gBAAM,cAAc,cAAc,IAAI,iCAAiC,CAAC,MAAM,IAAI,KAAK,IAAI,SAAS,GAAY,uBAAuB;AACvI,iBAAO,QAAQ,MAAM,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC,GAAG,CAAC,WAAW,CAAC;AAAA,QACpG,OAAO;AACL,gBAAM,OAAO;AACb,gBAAM,YAAY,MAAM,aAAa;AACrC,gBAAM,cAAc,cAAc,IAAI,oCAAoC,CAAC,KAAK,IAAI,SAAS,GAAY,uBAAuB;AAChI,iBAAO,QAAQ,MAAM,YAAY,IAAI,CAACA,UAAS,IAAI,0BAA0BA,KAAI,CAAC,GAAG,CAAC,WAAW,CAAC;AAAA,QACpG;AAAA,MACF;AAAA,MACA,MAAM,cAAc,qBAAoC,cAA4D;AAClH,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,gBAAM,cAAc,MAAM,KAAK,gBAAgB,KAAK;AACpD,iBAAO,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,YAAY,KAAK;AAAA,QAC3D,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,cAAc,MAAM,KAAK,gBAAgB;AAC/C,iBAAO,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,KAAK;AAAA,QAClD;AAAA,MACF;AAAA,MACA,cAAc,qBAAoC,cAAmD;AACnG,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,gBAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,iBAAO,QAAQ,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,YAAY,KAAK,MAAM,CAAC,aAAa,YAAY,CAAC;AAAA,QAC1G,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,cAAc,KAAK,eAAe;AACxC,iBAAO,QAAQ,MAAM,YAAY,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,KAAK,MAAM,CAAC,aAAa,GAAG,CAAC;AAAA,QACxF;AAAA,MACF;AAAA,MACA,MAAM,cAAc,qBAAoC,cAAyC;AAC/F,YAAI,uBAAuB,OAAO,wBAAwB,UAAU;AAClE,gBAAM,QAAQ;AACd,iBAAQ,MAAM,KAAK,cAAc,OAAO,YAAsB,MAAO;AAAA,QACvE,OAAO;AACL,gBAAM,MAAM;AACZ,iBAAQ,MAAM,KAAK,cAAc,GAAG,MAAO;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,MAAM,OAAO,QAAiC;AAC5C,cAAM,IAAI,kBAAkB,KAAK,IAAI,MAAM;AAAA,MAC7C;AAAA,MACA,MAAM,wBAAwB;AAC5B,eAAO,MAAM,IAAI,qBAAqB,yCAAyC,CAAC,CAAC;AAAA,MACnF;AAAA,MACA,MAAM,eAAe,SAAsD;AACzE,cAAM,SAAS,MAAM,IAAI,WAAW,eAAe,OAAO;AAC1D,cAAM,IAAI,iBAAiB,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC5C,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY,SAA+B;AAC/C,cAAM,SAAS,MAAM,KAAK,OAAO,OAAO;AACxC,cAAM,IAAI,iBAAiB,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC5C,eAAO;AAAA,MACT;AAAA,MACA,MAAM,eAAe,MAAY;AAC/B,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,4BAA4B,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE,GAAG,YAAY,CAAC;AAC/G,eAAO,IAAI,mCAAmC,MAAM;AAAA,MACtD;AAAA,MACA,eAAe,MAAY;AACzB,cAAM,SAAS,cAAc,IAAI,6BAA6B,CAAC,KAAK,IAAI,KAAK,EAAE,GAAY,uBAAuB;AAClH,eAAO,QAAQ,MAAM,IAAI,mCAAmC,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,MAC/E;AAAA,MACA,MAAM,sBAAsB;AAC1B,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,4BAA4B,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AACtG,eAAO,OAAO,IAAI,CAAC,SAAS,IAAI,8BAA8B,KAAK,IAAI,IAAI,CAAC;AAAA,MAC9E;AAAA,MACA,qBAAqB;AACnB,cAAM,SAAS,cAAc,IAAI,6BAA6B,CAAC,KAAK,EAAE,GAAY,2BAA2B;AAC7G,eAAO,QAAQ,MAAM,OAAO,IAAI,CAAC,SAAS,IAAI,8BAA8B,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;AAAA,MACvG;AAAA,MACA,sBAAsB,OAAO,SAA4C;AACvE,cAAM,iBAAiB,MAAM,IAAI,WAAW,2BAA2B,wCAAwC,KAAK,IAAI,IAAI,CAAC;AAC7H,cAAM,QAAQ,IAAI;AAAA,UAChB,IAAI,4BAA4B,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,UACjD,IAAI,iBAAiB,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,QACxC,CAAC;AACD,eAAO,IAAI,8BAA8B,KAAK,IAAI,cAAc;AAAA,MAClE;AAAA,MACA,aAAa;AACX,cAAM,SAAS,cAAc,IAAI,yBAAyB,CAAC,KAAK,EAAE,GAAY,mBAAmB;AACjG,eAAO,OAAO,IAAI,CAAC,WAAW,IAAI,sBAAsB,MAAM,CAAC;AAAA,MACjE;AAAA,MACA,MAAM,cAAc;AAClB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,wBAAwB,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AAClG,eAAO,OAAO,IAAI,CAAC,WAAW,IAAI,sBAAsB,MAAM,CAAC;AAAA,MACjE;AAAA,MACA,MAAM,aAAa,SAAwC;AACzD,cAAM,SAAS,MAAM,IAAI,WAAW;AAAA,UAClC,MAAM,4BAA4B,QAAQ,KAAK,IAAI,OAAO;AAAA,UAC1D;AAAA,UACA;AAAA,QACF;AACA,cAAM,IAAI,wBAAwB,QAAQ,CAAC,KAAK,EAAE,CAAC;AACnD,eAAO,IAAI,sBAAsB,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EAEU,wBAAwB,MAAgE;AAChG,WAAO;AAAA,MACL,GAAG,KAAK,oBAAoB,KAAK,IAAI;AAAA,MACrC,aAAa;AAAA,QACX,aAAa,KAAK;AAAA,QAClB,iBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA,EAEU,8BAA8B,MAA4D;AAClG,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,gBAAgB,KAAK;AAAA,MACrB,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,QAAQ,YAAY;AAClB,cAAM,KAAK,WAAW,2BAA2B,KAAK,IAAI,KAAK,OAAO;AAAA,MACxE;AAAA,IACF;AAAA,EACF;AAAA,EAEmB,qBAAqB,MAAmC,SAA+D;AACxI,UAAM,MAAM;AACZ,UAAM,cAAc;AAAA,MAClB,GAAG,KAAK,oBAAoB,IAAI;AAAA,MAChC,GAAG,KAAK,YAAY,OAAO;AAAA,MAC3B,GAAG,KAAK,mBAAmB,IAAI,KAAK,yBAAyB,OAAO,IAAI,CAAC;AAAA,IAC3E;AAEA,WAAO,OAAO,WAAW;AACzB,WAAO;AAAA,EACT;AAAA,EAEU,oBAAoB,MAA+C;AAC3E,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,iBAAiB,KAAK;AAAA,MACtB,WAAW,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC1C,gBAAgB,KAAK;AAAA,MACrB,wBAAwB,KAAK;AAAA,MAC7B,gBAAgB,KAAK;AAAA,MACrB,MAAM,OAAO,QAA0C;AACrD,cAAM,IAAI,WAAW,iBAAiB,KAAK,IAAI,8BAA8B,MAAM,CAAC;AACpF,cAAM,IAAI,kBAAkB,QAAQ,CAAC,MAAS,CAAC;AAAA,MACjD;AAAA,MACA,MAAM,SAAS;AACb,cAAM,IAAI,WAAW,iBAAiB,KAAK,EAAE;AAC7C,cAAM,IAAI,kBAAkB,QAAQ,CAAC,MAAS,CAAC;AAAA,MACjD;AAAA,MACA,MAAM,YAAY;AAChB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,+BAA+B,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AACzG,eAAO,OAAO,IAAI,OAAK,IAAI,wBAAwB,CAAC,CAAC;AAAA,MACvD;AAAA,MACA,WAAW;AACT,cAAM,SAAS,cAAc,IAAI,gCAAgC,CAAC,KAAK,EAAE,GAAY,iBAAiB;AACtG,eAAO,QAAQ,MAAM,OAAO,IAAI,OAAK,IAAI,wBAAwB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;AAAA,MAChF;AAAA,MACA,MAAM,QAAQ,QAAQ;AACpB,cAAM,IAAI,WAAW,oBAAoB;AAAA,UACvC,QAAQ,KAAK;AAAA,UACb;AAAA,QACF,CAAC;AACD,cAAM,IAAI,+BAA+B,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,MAC5D;AAAA,MACA,MAAM,WAAW,QAAQ;AACvB,cAAM,IAAI,WAAW,yBAAyB;AAAA,UAC5C,QAAQ,KAAK;AAAA,UACb;AAAA,QACF,CAAC;AACD,cAAM,IAAI,+BAA+B,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,MAC5D;AAAA,MACA,MAAM,WAAW,SAAkD;AACjE,cAAM,IAAI,WAAW,yBAAyB;AAAA,UAC5C,QAAQ,KAAK;AAAA,UACb,OAAO,QAAQ;AAAA,UACf,aAAa,QAAQ,eAAe,qBAAqB,IAAI,KAAK,gBAAgB,aAAa;AAAA,QACjG,CAAC;AACD,cAAM,IAAI,4BAA4B,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,MACzD;AAAA,MACA,MAAM,kBAAkB;AACtB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,4BAA4B,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AACtG,eAAO,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8BA,KAAI,CAAC;AAAA,MACrE;AAAA,MACA,iBAAiB;AACf,cAAM,SAAS,cAAc,IAAI,6BAA6B,CAAC,KAAK,EAAE,GAAY,uBAAuB;AACzG,eAAO,QAAQ,MAAM,OAAO,IAAI,CAACA,UAAS,IAAI,8BAA8BA,KAAI,CAAC,GAAG,CAAC,MAAM,CAAC;AAAA,MAC9F;AAAA,MACA,aAAa;AACX,cAAM,SAAS,cAAc,IAAI,yBAAyB,CAAC,KAAK,EAAE,GAAY,mBAAmB;AACjG,eAAO,OAAO,IAAI,CAAC,WAAW,IAAI,sBAAsB,MAAM,CAAC;AAAA,MACjE;AAAA,MACA,MAAM,cAAc;AAClB,cAAM,SAAS,OAAO,QAAQ,MAAM,IAAI,wBAAwB,UAAU,CAAC,KAAK,EAAE,GAAG,YAAY,CAAC;AAClG,eAAO,OAAO,IAAI,CAAC,WAAW,IAAI,sBAAsB,MAAM,CAAC;AAAA,MACjE;AAAA,MACA,MAAM,aAAa,SAAwC;AACzD,cAAM,SAAS,MAAM,IAAI,WAAW;AAAA,UAClC,MAAM,4BAA4B,QAAQ,KAAK,IAAI,OAAO;AAAA,UAC1D;AAAA,UACA;AAAA,QACF;AACA,cAAM,IAAI,wBAAwB,QAAQ,CAAC,KAAK,EAAE,CAAC;AACnD,eAAO,IAAI,sBAAsB,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAgB,eAAe,SAA6D;AAC1F,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,wBAAwB,UAAU,CAAC,QAAQ,QAAQ,MAAM,GAAG,YAAY,CAAC;AAChH,WAAO,OAAO,KAAK,sBAAsB,IAAI,IAAI;AAAA,EACnD;AAAA,EAEA,MAAgB,eAAe,SAA6D;AAC1F,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,wBAAwB,UAAU,CAAC,QAAQ,QAAQ,MAAM,GAAG,YAAY,CAAC;AAChH,WAAO,OAAO,KAAK,sBAAsB,IAAI,IAAI;AAAA,EACnD;AAAA,EACU,eAAe,SAAoD;AAC3E,UAAM,OAAO,cAAc,KAAK,yBAAyB,CAAC,QAAQ,QAAQ,MAAM,GAAY,iBAAiB;AAC7G,WAAO,QAAQ,MAAM,OAAO,KAAK,sBAAsB,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC;AAAA,EAC7E;AAAA,EACU,eAAe,SAAoD;AAC3E,UAAM,OAAO,cAAc,KAAK,yBAAyB,CAAC,QAAQ,QAAQ,MAAM,GAAY,iBAAiB;AAC7G,WAAO,QAAQ,MAAM,OAAO,KAAK,sBAAsB,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC;AAAA,EAC7E;AAAA,EACA,MAAgB,iBAAiB,QAA4C;AAC3E,UAAM,eAAe,MAAM,KAAK,eAAe,EAAE,OAAO,CAAC;AACzD,QAAI,iBAAiB,MAAM;AACzB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,KAAK,kBAAkB,aAAa,MAAM;AAAA,EACzD;AAAA,EACU,iBAAiB,QAAmC;AAC5D,UAAM,eAAe,KAAK,eAAe,EAAE,OAAO,CAAC;AACnD,QAAI,iBAAiB,MAAM;AACzB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,YAAY,aAAa,MAAM;AAAA,EAC7C;AAAA,EAEA,MAAgB,iBAAiB,QAA4C;AAC3E,UAAM,eAAe,MAAM,KAAK,eAAe,EAAE,OAAO,CAAC;AACzD,QAAI,iBAAiB,MAAM;AACzB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,KAAK,QAAQ,aAAa,MAAM;AAAA,EAC/C;AAAA,EACU,iBAAiB,QAAmC;AAC5D,UAAM,eAAe,KAAK,eAAe,EAAE,OAAO,CAAC;AACnD,QAAI,iBAAiB,MAAM;AACzB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,aAAa,MAAM;AAAA,EACzC;AAAA,EAEA,MAAM,WAAW,SAAuD;AACtE,UAAM,OAAO,MAAM,KAAK,WAAW,iBAAiB,8BAA8B,OAAO,CAAC;AAC1F,UAAM,KAAK,cAAc;AACzB,WAAO,KAAK,oBAAoB,IAAI;AAAA,EACtC;AAAA,EAQA,MAAM,QAAQ,SAAyI;AACrJ,QAAI,OAAO,YAAY,UAAU;AAC/B,aAAO,MAAM,KAAK,kBAAkB,OAAO;AAAA,IAC7C,WAAW,OAAO,YAAY,YAAY,YAAY,SAAS;AAC7D,aAAO,MAAM,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IACnD,OAAO;AAEL,WAAK,4BAA4B,SAAS,UAAU;AACpD,YAAM,UAAU,MAAM,KAAK,YAAY,SAAS,UAAU;AAC1D,UAAI,OAAO,OAAO,QAAQ,MAAM,KAAK,wBAAwB,UAAU,CAAC,OAAO,GAAG,YAAY,CAAC;AAC/F,UAAI,MAAM,gBAAgB,SAAS,OAAO,eAAe,SAAS,OAAO,uBAAuB;AAC9F,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,MAAM;AACjB,gBAAQ,SAAS,IAAI;AAAA,UACnB,KAAK,YAAY;AACf,kBAAM,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC7C;AAAA,UACF;AAAA,UACA,KAAK,SAAS;AACZ,kBAAM,IAAI,MAAM,mEAAmE;AAAA,UACrF;AAAA,UACA,KAAK,aAAa;AAChB,kBAAM,SAAS,MAAM,KAAK,mBAAmB;AAC7C,mBAAO,MAAM,KAAK,QAAQ,EAAE,YAAY,QAAQ,IAAI,sBAAsB,CAAC,KAAK,SAAS,mDAAmD;AAAA,UAC9I;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,eAAe;AAClB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO,QAAQ,KAAK,qBAAqB,MAAM,OAAO;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,MAAM,gBAAqE;AACzE,YAAQ,KAAK,gFAAgF;AAC7F,WAAO,MAAM,KAAK,QAAQ;AAAA,EAC5B;AAAA,EAEA,MAAM,kBAAkB,QAA4C;AAClE,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,iBAAiB,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;AACzF,WAAO,QAAQ,KAAK,oBAAoB,IAAI;AAAA,EAC9C;AAAA,EAQA,QAAQ,SAAgI;AACtI,QAAI,OAAO,YAAY,UAAU;AAC/B,aAAO,KAAK,YAAY,OAAO;AAAA,IACjC,WAAW,OAAO,YAAY,YAAY,YAAY,SAAS;AAC7D,aAAO,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IAC7C,OAAO;AAEL,WAAK,4BAA4B,SAAS,UAAU;AAEpD,YAAM,UAAU,KAAK,YAAY,SAAS,UAAU;AACpD,UAAI,OAAO,cAAc,KAAK,yBAAyB,CAAC,OAAO,GAAY,WAAW;AACtF,UAAI,MAAM,gBAAgB,SAAS,OAAO,eAAe,SAAS,OAAO,uBAAuB;AAC9F,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,MAAM;AACjB,gBAAQ,SAAS,IAAI;AAAA,UACnB,KAAK,YAAY;AACf,8BAAkB,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC,CAAC;AAC1D,oBAAQ;AACR,kBAAM,IAAI,oBAAoB,6BAA6B;AAAA,UAC7D;AAAA,UACA,KAAK,SAAS;AACZ,kBAAM,IAAI,MAAM,mEAAmE;AAAA,UACrF;AAAA,UACA,KAAK,aAAa;AAGhB,8BAAkB,YAAY;AAC5B,oBAAM,KAAK,mBAAmB;AAC9B,kBAAI,OAAO,WAAW,aAAa;AACjC,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF,CAAC;AACD,oBAAQ;AACR,kBAAM,IAAI,oBAAoB,6BAA6B;AAAA,UAC7D;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,eAAe;AAAA,UAEpB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,QAAQ,MAAM;AACnB,eAAO,QAAQ,KAAK,qBAAqB,MAAM,OAAO;AAAA,MACxD,GAAG,CAAC,MAAM,SAAS,SAAS,EAAE,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,YAAY,QAAmC;AAC7C,UAAM,OAAO,cAAc,KAAK,kBAAkB,CAAC,MAAM,GAAG,eAAe;AAC3E,WAAO,QAAQ,MAAM;AACnB,aAAO,QAAQ,KAAK,oBAAoB,IAAI;AAAA,IAC9C,GAAG,CAAC,IAAI,CAAC;AAAA,EACX;AAAA,EAEA,MAAM,UAAU,SAAyF;AACvG,UAAM,OAAO,OAAO,QAAQ,MAAM,KAAK,kBAAkB,UAAU,CAAC,SAAS,QAAQ,SAAS,OAAO,SAAS,SAAS,SAAS,MAAM,SAAS,KAAK,GAAG,YAAY,CAAC;AACpK,UAAM,SAAc,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC;AACrE,WAAO,aAAa,KAAK,YAAY,eAAe;AACpD,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,SAAgF;AACvF,UAAM,OAAO,cAAc,KAAK,mBAAmB,CAAC,SAAS,QAAQ,SAAS,OAAO,SAAS,SAAS,SAAS,MAAM,SAAS,KAAK,GAAY,kBAAkB;AAClK,UAAM,SAAc,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC;AACrE,WAAO,aAAa,KAAK,YAAY,eAAe;AACpD,WAAO;AAAA,EACT;AAAA,EAEA,0BAA0B,MAA6G;AACrI,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,IACX;AAAA,EACF;AAAA,EAEA,wCAAwC,MAAqF;AAC3H,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,wBAAwB,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA,EAEA,2CAA2C,MAA2F;AACpI,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,aAAa,KAAK;AAAA,MAClB,wBAAwB,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA,EAEA,MAAM,YAAmC;AACvC,UAAM,QAAQ,OAAO,QAAQ,MAAM,KAAK,kBAAkB,UAAU,CAAC,MAAS,GAAG,YAAY,CAAC;AAC9F,WAAO,MAAM,IAAI,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC;AAAA,EACrD;AAAA,EAEA,MAAM,WAAW,MAAoD;AACnE,UAAM,OAAO,MAAM,KAAK,WAAW,iBAAiB,8BAA8B,IAAI,CAAC;AACvF,UAAM,KAAK,kBAAkB,QAAQ,CAAC,MAAS,CAAC;AAChD,WAAO,KAAK,oBAAoB,IAAI;AAAA,EACtC;AAAA,EAEA,WAAyB;AACvB,UAAM,QAAQ,cAAc,KAAK,mBAAmB,CAAC,MAAS,GAAG,kBAAkB;AACnF,WAAO,QAAQ,MAAM;AACnB,aAAO,MAAM,IAAI,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACrD,GAAG,CAAC,KAAK,CAAC;AAAA,EACZ;AAAA,EAIA,MAAM,QAAQ,SAAmE;AAC/E,QAAI,OAAO,YAAY,YAAY,YAAY,SAAS;AACtD,aAAO,MAAM,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IACnD,OAAO;AACL,YAAM,SAAS;AACf,YAAM,QAAQ,MAAM,KAAK,UAAU;AACnC,aAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,IAC/C;AAAA,EACF;AAAA,EAIA,QAAQ,SAA0D;AAChE,QAAI,OAAO,YAAY,YAAY,YAAY,SAAS;AACtD,aAAO,KAAK,iBAAiB,QAAQ,MAAM;AAAA,IAC7C,OAAO;AACL,YAAM,SAAS;AACf,YAAM,QAAQ,KAAK,SAAS;AAC5B,aAAO,QAAQ,MAAM;AACnB,eAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,KAAK;AAAA,MAC/C,GAAG,CAAC,OAAO,MAAM,CAAC;AAAA,IACpB;AAAA,EACF;AAAA,EAEA,MAAyB,gBAAgB,SAA0B;AACjE,UAAM,QAAQ,IAAI;AAAA,MAChB,MAAM,aAAa,OAAO;AAAA,MAC1B,KAAK,wBAAwB,QAAQ,CAAC,OAAO,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAyB,gBAAgB;AACvC,UAAM,QAAQ,IAAI;AAAA,MAChB,MAAM,cAAc;AAAA,MACpB,KAAK,iBAAiB,aAAa,MAAM,IAAI;AAAA,MAC7C,KAAK,kBAAkB,aAAa,MAAM,IAAI;AAAA,MAC9C,KAAK,4BAA4B,aAAa,MAAM,IAAI;AAAA,IAC1D,CAAC;AAAA,EACH;AACF;", "names": ["crud"]}