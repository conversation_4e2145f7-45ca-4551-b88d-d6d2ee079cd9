{"version": 3, "sources": ["../../../../../../src/lib/stack-app/apps/implementations/index.ts"], "sourcesContent": ["\n//===========================================\n// THIS FILE IS AUTO-GENERATED FROM TEMPLATE. DO NOT EDIT IT DIRECTLY\n//===========================================\n\nimport { scrambleDuringCompileTime } from \"@stackframe/stack-shared/dist/utils/compile-time\";\nimport { _StackAdminAppImplIncomplete } from \"./admin-app-impl\";\nimport { _StackClientAppImplIncomplete } from \"./client-app-impl\";\nimport { _StackServerAppImplIncomplete } from \"./server-app-impl\";\n\n\n/**\n * Prevents a circular dependency between the client and admin apps. For more information, see the documentation comment\n * of `_StackClientAppImplIncomplete.LazyStackAdminAppImpl`.\n *\n * Note: This is an explicitly defined function that returns the new values (and not a barrel file with top-level side\n * effects) because we have `sideEffects: false` in the package.json, and so it would be tree-shaken away if we just\n * exported the values directly.\n */\nfunction complete() {\n  _StackClientAppImplIncomplete.LazyStackAdminAppImpl.value = _StackAdminAppImplIncomplete;\n\n  return {\n    _StackAdminAppImpl: scrambleDuringCompileTime(_StackAdminAppImplIncomplete),\n    _StackClientAppImpl: scrambleDuringCompileTime(_StackClientAppImplIncomplete),\n    _StackServerAppImpl: scrambleDuringCompileTime(_StackServerAppImplIncomplete),\n  };\n}\n\nexport const {\n  _StackAdminAppImpl,\n  _StackClientAppImpl,\n  _StackServerAppImpl\n} = complete();\n\n"], "mappings": ";AAKA,SAAS,iCAAiC;AAC1C,SAAS,oCAAoC;AAC7C,SAAS,qCAAqC;AAC9C,SAAS,qCAAqC;AAW9C,SAAS,WAAW;AAClB,gCAA8B,sBAAsB,QAAQ;AAE5D,SAAO;AAAA,IACL,oBAAoB,0BAA0B,4BAA4B;AAAA,IAC1E,qBAAqB,0BAA0B,6BAA6B;AAAA,IAC5E,qBAAqB,0BAA0B,6BAA6B;AAAA,EAC9E;AACF;AAEO,IAAM;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,IAAI,SAAS;", "names": []}